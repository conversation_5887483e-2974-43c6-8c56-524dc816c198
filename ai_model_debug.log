2025-07-13 19:30:24,062 [INFO] AIModel: === AI MODEL INITIALIZATION ===
2025-07-13 19:30:24,062 [INFO] AIModel: Initializing mood manager...
2025-07-13 19:30:24,064 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-13 19:30:24,462 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/config.json HTTP/1.1" 307 0
2025-07-13 19:30:24,512 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/nateraw/bert-base-uncased-emotion/064d252021b51d95cd0547c89c6489100da0dc4c/config.json HTTP/1.1" 200 0
2025-07-13 19:30:24,936 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/model.safetensors HTTP/1.1" 404 0
2025-07-13 19:30:24,950 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-13 19:30:25,227 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion HTTP/1.1" 200 3676
2025-07-13 19:30:25,262 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-13 19:30:25,296 [INFO] AIModel: Initializing memory system...
2025-07-13 19:30:25,296 [INFO] sentence_transformers.SentenceTransformer: Use pytorch device_name: cpu
2025-07-13 19:30:25,298 [INFO] sentence_transformers.SentenceTransformer: Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-13 19:30:25,479 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-13 19:30:25,503 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/main HTTP/1.1" 200 2736
2025-07-13 19:30:25,532 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-13 19:30:25,701 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/discussions?p=0 HTTP/1.1" 200 1434
2025-07-13 19:30:25,715 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-13 19:30:25,768 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-13 19:30:25,911 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/refs%2Fpr%2F1 HTTP/1.1" 200 3701
2025-07-13 19:30:25,962 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-13 19:30:26,013 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-13 19:30:26,092 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors.index.json HTTP/1.1" 404 0
2025-07-13 19:30:26,217 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/README.md HTTP/1.1" 307 0
2025-07-13 19:30:26,266 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/README.md HTTP/1.1" 200 0
2025-07-13 19:30:26,279 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors HTTP/1.1" 302 0
2025-07-13 19:30:26,695 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-13 19:30:26,744 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-13 19:30:26,932 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/sentence_bert_config.json HTTP/1.1" 307 0
2025-07-13 19:30:26,982 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/sentence_bert_config.json HTTP/1.1" 200 0
2025-07-13 19:30:27,168 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/adapter_config.json HTTP/1.1" 404 0
2025-07-13 19:30:27,353 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config.json HTTP/1.1" 307 0
2025-07-13 19:30:27,407 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config.json HTTP/1.1" 200 0
2025-07-13 19:30:27,890 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/tokenizer_config.json HTTP/1.1" 307 0
2025-07-13 19:30:27,942 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/tokenizer_config.json HTTP/1.1" 200 0
2025-07-13 19:30:28,133 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-13 19:30:28,338 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/1_Pooling/config.json HTTP/1.1" 307 0
2025-07-13 19:30:28,386 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/1_Pooling%2Fconfig.json HTTP/1.1" 200 0
2025-07-13 19:30:28,579 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2 HTTP/1.1" 200 6821
2025-07-13 19:30:28,585 [INFO] memory: Connected to database: memory.db
2025-07-13 19:30:28,586 [DEBUG] memory: Memory table created/verified
2025-07-13 19:30:28,587 [INFO] memory: Loaded FAISS index with 11 vectors
2025-07-13 19:30:28,587 [INFO] memory: SemanticMemory initialized with 11 existing memories
2025-07-13 19:30:28,589 [INFO] AIModel: Starting model loading...
2025-07-13 19:30:28,589 [INFO] AIModel: === MODEL LOADING DEBUG ===
2025-07-13 19:30:28,589 [INFO] AIModel: Model path: D:\MythoMist\mythomist-7b.Q4_K_M.gguf
2025-07-13 19:30:28,590 [INFO] AIModel: Model type: mistral
2025-07-13 19:30:28,590 [INFO] AIModel: GPU layers: 0
2025-07-13 19:30:28,590 [INFO] AIModel: Model file size: 4166.07 MB
2025-07-13 19:30:28,590 [INFO] AIModel: === ATTEMPT 1 ===
2025-07-13 19:30:28,590 [INFO] AIModel: Config: {'gpu_layers': 0, 'threads': 1}
2025-07-13 19:30:28,590 [INFO] AIModel: Creating model instance...
2025-07-13 19:30:30,232 [INFO] AIModel: Memory system ready with 11 existing memories
2025-07-13 19:30:30,233 [WARNING] discord.client: PyNaCl is not installed, voice will NOT be supported
2025-07-13 19:30:30,234 [DEBUG] discord.client: on_ready has successfully been registered as an event
2025-07-13 19:30:30,235 [DEBUG] discord.client: on_message has successfully been registered as an event
2025-07-13 19:30:30,235 [DEBUG] asyncio: Using proactor: IocpProactor
2025-07-13 19:30:30,236 [INFO] discord.client: logging in using static token
2025-07-13 19:30:31,585 [INFO] discord.gateway: Shard ID None has connected to Gateway (Session ID: f7067fd32787c7c7d62659b2a35644bd).
2025-07-13 19:30:47,036 [DEBUG] memory: Found 2 similar memories
2025-07-13 19:31:14,743 [DEBUG] memory: Found 2 similar memories
2025-07-13 19:31:15,023 [ERROR] AIModel: Generation error: exception: access violation reading 0x0000000000000000
2025-07-13 19:31:15,023 [ERROR] AIModel: Generation error: exception: access violation reading 0x0000000000000000
2025-07-13 19:40:42,213 [ERROR] memory: Error saving FAISS index: name 'open' is not defined
2025-07-13 19:40:42,213 [INFO] memory: Semantic memory system closed
2025-07-13 19:40:47,634 [INFO] AIModel: === AI MODEL INITIALIZATION ===
2025-07-13 19:40:47,636 [INFO] AIModel: Initializing mood manager...
2025-07-13 19:40:47,708 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-13 19:40:48,370 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/config.json HTTP/1.1" 307 0
2025-07-13 19:40:48,421 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/nateraw/bert-base-uncased-emotion/064d252021b51d95cd0547c89c6489100da0dc4c/config.json HTTP/1.1" 200 0
2025-07-13 19:40:49,022 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/model.safetensors HTTP/1.1" 404 0
2025-07-13 19:40:49,029 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-13 19:40:49,313 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion HTTP/1.1" 200 3678
2025-07-13 19:40:49,391 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-13 19:40:49,447 [INFO] AIModel: Initializing memory system...
2025-07-13 19:40:49,449 [INFO] sentence_transformers.SentenceTransformer: Use pytorch device_name: cpu
2025-07-13 19:40:49,449 [INFO] sentence_transformers.SentenceTransformer: Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-13 19:40:49,524 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/main HTTP/1.1" 200 2736
2025-07-13 19:40:49,631 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-13 19:40:49,683 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-13 19:40:49,865 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-13 19:40:49,918 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-13 19:40:49,969 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/discussions?p=0 HTTP/1.1" 200 1434
2025-07-13 19:40:50,162 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/refs%2Fpr%2F1 HTTP/1.1" 200 3701
2025-07-13 19:40:50,345 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-13 19:40:50,346 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors.index.json HTTP/1.1" 404 0
2025-07-13 19:40:50,396 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-13 19:40:50,536 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors HTTP/1.1" 302 0
2025-07-13 19:40:50,589 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/README.md HTTP/1.1" 307 0
2025-07-13 19:40:50,640 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/README.md HTTP/1.1" 200 0
2025-07-13 19:40:50,823 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-13 19:40:50,873 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-13 19:40:51,057 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/sentence_bert_config.json HTTP/1.1" 307 0
2025-07-13 19:40:51,106 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/sentence_bert_config.json HTTP/1.1" 200 0
2025-07-13 19:40:51,295 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/adapter_config.json HTTP/1.1" 404 0
2025-07-13 19:40:51,487 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config.json HTTP/1.1" 307 0
2025-07-13 19:40:51,543 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config.json HTTP/1.1" 200 0
2025-07-13 19:40:52,376 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/tokenizer_config.json HTTP/1.1" 307 0
2025-07-13 19:40:52,425 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/tokenizer_config.json HTTP/1.1" 200 0
2025-07-13 19:40:52,608 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-13 19:40:52,819 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/1_Pooling/config.json HTTP/1.1" 307 0
2025-07-13 19:40:52,870 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/1_Pooling%2Fconfig.json HTTP/1.1" 200 0
2025-07-13 19:40:53,066 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2 HTTP/1.1" 200 6821
2025-07-13 19:40:53,078 [INFO] memory: Connected to database: memory.db
2025-07-13 19:40:53,078 [DEBUG] memory: Memory table created/verified
2025-07-13 19:40:53,085 [INFO] memory: Loaded FAISS index with 11 vectors
2025-07-13 19:40:53,086 [INFO] memory: SemanticMemory initialized with 11 existing memories
2025-07-13 19:40:53,087 [INFO] AIModel: Starting model loading...
2025-07-13 19:40:53,087 [INFO] AIModel: === MODEL LOADING DEBUG ===
2025-07-13 19:40:53,088 [INFO] AIModel: Model path: D:\MythoMist\mythomist-7b.Q4_K_M.gguf
2025-07-13 19:40:53,088 [INFO] AIModel: Model type: mistral
2025-07-13 19:40:53,088 [INFO] AIModel: GPU layers: 50
2025-07-13 19:40:53,088 [INFO] AIModel: Model file size: 4166.07 MB
2025-07-13 19:40:53,093 [WARNING] discord.client: PyNaCl is not installed, voice will NOT be supported
2025-07-13 19:40:53,094 [DEBUG] discord.client: on_ready has successfully been registered as an event
2025-07-13 19:40:53,094 [DEBUG] discord.client: on_message has successfully been registered as an event
2025-07-13 19:40:53,094 [DEBUG] asyncio: Using proactor: IocpProactor
2025-07-13 19:40:53,095 [INFO] discord.client: logging in using static token
2025-07-13 19:40:54,619 [INFO] discord.gateway: Shard ID None has connected to Gateway (Session ID: 32a78e8ced6858c21bdb55c9feef15b7).
2025-07-13 19:42:35,280 [ERROR] memory: Error saving FAISS index: name 'open' is not defined
2025-07-13 19:42:35,280 [INFO] memory: Semantic memory system closed
2025-07-13 19:43:31,021 [INFO] AIModel: === AI MODEL INITIALIZATION ===
2025-07-13 19:43:31,021 [INFO] AIModel: Initializing mood manager...
2025-07-13 19:43:31,099 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-13 19:43:31,697 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/config.json HTTP/1.1" 307 0
2025-07-13 19:43:31,749 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/nateraw/bert-base-uncased-emotion/064d252021b51d95cd0547c89c6489100da0dc4c/config.json HTTP/1.1" 200 0
2025-07-13 19:43:35,579 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/model.safetensors HTTP/1.1" 404 0
2025-07-13 19:43:35,584 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-13 19:43:35,870 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion HTTP/1.1" 200 3678
2025-07-13 19:43:35,922 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-13 19:43:35,987 [INFO] AIModel: Initializing memory system...
2025-07-13 19:43:35,988 [INFO] sentence_transformers.SentenceTransformer: Use pytorch device_name: cpu
2025-07-13 19:43:35,988 [INFO] sentence_transformers.SentenceTransformer: Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-13 19:43:36,080 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/main HTTP/1.1" 200 2736
2025-07-13 19:43:36,176 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-13 19:43:36,228 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-13 19:43:36,414 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-13 19:43:36,463 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-13 19:43:36,537 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/discussions?p=0 HTTP/1.1" 200 1434
2025-07-13 19:43:36,651 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-13 19:43:36,702 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-13 19:43:36,751 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/refs%2Fpr%2F1 HTTP/1.1" 200 3701
2025-07-13 19:43:36,887 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/README.md HTTP/1.1" 307 0
2025-07-13 19:43:36,933 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/README.md HTTP/1.1" 200 0
2025-07-13 19:43:36,945 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors.index.json HTTP/1.1" 404 0
2025-07-13 19:43:37,131 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-13 19:43:37,133 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors HTTP/1.1" 302 0
2025-07-13 19:43:37,178 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-13 19:43:37,368 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/sentence_bert_config.json HTTP/1.1" 307 0
2025-07-13 19:43:37,415 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/sentence_bert_config.json HTTP/1.1" 200 0
2025-07-13 19:43:37,607 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/adapter_config.json HTTP/1.1" 404 0
2025-07-13 19:43:37,793 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config.json HTTP/1.1" 307 0
2025-07-13 19:43:37,843 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config.json HTTP/1.1" 200 0
2025-07-13 19:43:44,592 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/tokenizer_config.json HTTP/1.1" 307 0
2025-07-13 19:43:44,638 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/tokenizer_config.json HTTP/1.1" 200 0
2025-07-13 19:43:44,836 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-13 19:43:45,060 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/1_Pooling/config.json HTTP/1.1" 307 0
2025-07-13 19:43:45,109 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/1_Pooling%2Fconfig.json HTTP/1.1" 200 0
2025-07-13 19:43:45,381 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2 HTTP/1.1" 200 6821
2025-07-13 19:43:45,388 [INFO] memory: Connected to database: memory.db
2025-07-13 19:43:45,389 [DEBUG] memory: Memory table created/verified
2025-07-13 19:43:45,392 [INFO] memory: Loaded FAISS index with 11 vectors
2025-07-13 19:43:45,393 [INFO] memory: SemanticMemory initialized with 11 existing memories
2025-07-13 19:43:45,399 [INFO] AIModel: Starting model loading...
2025-07-13 19:43:45,401 [INFO] AIModel: === MODEL LOADING DEBUG ===
2025-07-13 19:43:45,401 [INFO] AIModel: Model path: D:\MythoMist\mythomist-7b.Q4_K_M.gguf
2025-07-13 19:43:45,401 [INFO] AIModel: Model type: mistral
2025-07-13 19:43:45,402 [INFO] AIModel: GPU layers: 50
2025-07-13 19:43:45,402 [INFO] AIModel: Model file size: 4166.07 MB
2025-07-13 19:43:45,415 [WARNING] discord.client: PyNaCl is not installed, voice will NOT be supported
2025-07-13 19:43:45,416 [DEBUG] discord.client: on_ready has successfully been registered as an event
2025-07-13 19:43:45,417 [DEBUG] discord.client: on_message has successfully been registered as an event
2025-07-13 19:43:45,417 [DEBUG] asyncio: Using proactor: IocpProactor
2025-07-13 19:43:45,418 [INFO] discord.client: logging in using static token
2025-07-13 19:43:46,617 [INFO] discord.gateway: Shard ID None has connected to Gateway (Session ID: b54ce19f6b795019e08b4dd5c11a38e6).
2025-07-13 19:47:02,531 [ERROR] memory: Error saving FAISS index: name 'open' is not defined
2025-07-13 19:47:02,531 [INFO] memory: Semantic memory system closed
2025-07-13 19:47:20,376 [INFO] AIModel: === AI MODEL INITIALIZATION ===
2025-07-13 19:47:20,376 [INFO] AIModel: Initializing mood manager...
2025-07-13 19:47:20,378 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-13 19:47:21,752 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/config.json HTTP/1.1" 307 0
2025-07-13 19:47:21,792 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/nateraw/bert-base-uncased-emotion/064d252021b51d95cd0547c89c6489100da0dc4c/config.json HTTP/1.1" 200 0
2025-07-13 19:47:22,233 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/model.safetensors HTTP/1.1" 404 0
2025-07-13 19:47:22,237 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-13 19:47:22,493 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion HTTP/1.1" 200 3678
2025-07-13 19:47:22,605 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-13 19:47:22,638 [INFO] AIModel: Initializing memory system...
2025-07-13 19:47:22,639 [INFO] sentence_transformers.SentenceTransformer: Use pytorch device_name: cpu
2025-07-13 19:47:22,639 [INFO] sentence_transformers.SentenceTransformer: Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-13 19:47:22,698 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/main HTTP/1.1" 200 2736
2025-07-13 19:47:22,839 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-13 19:47:22,878 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-13 19:47:22,973 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/discussions?p=0 HTTP/1.1" 200 1434
2025-07-13 19:47:23,064 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-13 19:47:23,104 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-13 19:47:23,178 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/refs%2Fpr%2F1 HTTP/1.1" 200 3701
2025-07-13 19:47:23,287 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-13 19:47:23,327 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-13 19:47:23,358 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors.index.json HTTP/1.1" 404 0
2025-07-13 19:47:23,509 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/README.md HTTP/1.1" 307 0
2025-07-13 19:47:23,549 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/README.md HTTP/1.1" 200 0
2025-07-13 19:47:23,728 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-13 19:47:23,773 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-13 19:47:23,801 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors HTTP/1.1" 302 0
2025-07-13 19:47:23,955 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/sentence_bert_config.json HTTP/1.1" 307 0
2025-07-13 19:47:23,995 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/sentence_bert_config.json HTTP/1.1" 200 0
2025-07-13 19:47:24,177 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/adapter_config.json HTTP/1.1" 404 0
2025-07-13 19:47:24,362 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config.json HTTP/1.1" 307 0
2025-07-13 19:47:24,402 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config.json HTTP/1.1" 200 0
2025-07-13 19:47:24,978 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/tokenizer_config.json HTTP/1.1" 307 0
2025-07-13 19:47:25,022 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/tokenizer_config.json HTTP/1.1" 200 0
2025-07-13 19:47:25,207 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-13 19:47:25,423 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/1_Pooling/config.json HTTP/1.1" 307 0
2025-07-13 19:47:25,466 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/1_Pooling%2Fconfig.json HTTP/1.1" 200 0
2025-07-13 19:47:25,649 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2 HTTP/1.1" 200 6821
2025-07-13 19:47:25,651 [INFO] memory: Connected to database: memory.db
2025-07-13 19:47:25,652 [DEBUG] memory: Memory table created/verified
2025-07-13 19:47:25,657 [INFO] memory: Loaded FAISS index with 11 vectors
2025-07-13 19:47:25,659 [INFO] memory: SemanticMemory initialized with 11 existing memories
2025-07-13 19:47:25,660 [INFO] AIModel: Starting model loading...
2025-07-13 19:47:25,660 [INFO] AIModel: === MODEL LOADING DEBUG ===
2025-07-13 19:47:25,660 [INFO] AIModel: Model path: D:\MythoMist\mythomist-7b.Q4_K_M.gguf
2025-07-13 19:47:25,660 [INFO] AIModel: Model type: mistral
2025-07-13 19:47:25,661 [INFO] AIModel: GPU layers: 50
2025-07-13 19:47:25,661 [INFO] AIModel: Model file size: 4166.07 MB
2025-07-13 19:47:25,665 [WARNING] discord.client: PyNaCl is not installed, voice will NOT be supported
2025-07-13 19:47:25,665 [DEBUG] discord.client: on_ready has successfully been registered as an event
2025-07-13 19:47:25,665 [DEBUG] discord.client: on_message has successfully been registered as an event
2025-07-13 19:47:25,666 [DEBUG] asyncio: Using proactor: IocpProactor
2025-07-13 19:47:25,666 [INFO] discord.client: logging in using static token
2025-07-13 19:47:26,900 [INFO] discord.gateway: Shard ID None has connected to Gateway (Session ID: dd9cac75323cc97db8e9e78a51c3f91b).
2025-07-13 19:48:57,126 [ERROR] memory: Error saving FAISS index: name 'open' is not defined
2025-07-13 19:48:57,126 [INFO] memory: Semantic memory system closed
2025-07-13 19:49:33,273 [INFO] AIModel: === AI MODEL INITIALIZATION ===
2025-07-13 19:49:33,273 [INFO] AIModel: Initializing mood manager...
2025-07-13 19:49:33,349 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-13 19:49:33,923 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/config.json HTTP/1.1" 307 0
2025-07-13 19:49:33,965 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/nateraw/bert-base-uncased-emotion/064d252021b51d95cd0547c89c6489100da0dc4c/config.json HTTP/1.1" 200 0
2025-07-13 19:49:34,332 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/model.safetensors HTTP/1.1" 404 0
2025-07-13 19:49:34,334 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-13 19:49:34,594 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion HTTP/1.1" 200 3678
2025-07-13 19:49:34,656 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-13 19:49:34,686 [INFO] AIModel: ✅ Mood manager initialized
2025-07-13 19:49:34,687 [INFO] AIModel: Initializing memory system...
2025-07-13 19:49:34,687 [INFO] sentence_transformers.SentenceTransformer: Use pytorch device_name: cpu
2025-07-13 19:49:34,687 [INFO] sentence_transformers.SentenceTransformer: Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-13 19:49:34,792 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/main HTTP/1.1" 200 2736
2025-07-13 19:49:35,136 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-13 19:49:35,177 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-13 19:49:35,242 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/discussions?p=0 HTTP/1.1" 200 1434
2025-07-13 19:49:35,358 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-13 19:49:35,399 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-13 19:49:35,462 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/refs%2Fpr%2F1 HTTP/1.1" 200 3701
2025-07-13 19:49:35,579 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-13 19:49:35,620 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-13 19:49:35,641 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors.index.json HTTP/1.1" 404 0
2025-07-13 19:49:35,801 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/README.md HTTP/1.1" 307 0
2025-07-13 19:49:35,823 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors HTTP/1.1" 302 0
2025-07-13 19:49:35,842 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/README.md HTTP/1.1" 200 0
2025-07-13 19:49:36,026 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-13 19:49:36,069 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-13 19:49:36,250 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/sentence_bert_config.json HTTP/1.1" 307 0
2025-07-13 19:49:36,292 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/sentence_bert_config.json HTTP/1.1" 200 0
2025-07-13 19:49:36,510 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/adapter_config.json HTTP/1.1" 404 0
2025-07-13 19:49:36,691 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config.json HTTP/1.1" 307 0
2025-07-13 19:49:36,734 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config.json HTTP/1.1" 200 0
2025-07-13 19:49:37,289 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/tokenizer_config.json HTTP/1.1" 307 0
2025-07-13 19:49:37,331 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/tokenizer_config.json HTTP/1.1" 200 0
2025-07-13 19:49:37,518 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-13 19:49:37,727 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/1_Pooling/config.json HTTP/1.1" 307 0
2025-07-13 19:49:37,768 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/1_Pooling%2Fconfig.json HTTP/1.1" 200 0
2025-07-13 19:49:37,951 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2 HTTP/1.1" 200 6821
2025-07-13 19:49:37,956 [INFO] memory: Connected to database: memory.db
2025-07-13 19:49:37,956 [DEBUG] memory: Memory table created/verified
2025-07-13 19:49:37,962 [INFO] memory: Loaded FAISS index with 11 vectors
2025-07-13 19:49:37,962 [INFO] memory: SemanticMemory initialized with 11 existing memories
2025-07-13 19:49:37,962 [INFO] AIModel: ✅ Memory system initialized
2025-07-13 19:49:37,962 [INFO] AIModel: Starting model loading...
2025-07-13 19:49:37,962 [INFO] AIModel: === MODEL LOADING DEBUG ===
2025-07-13 19:49:37,962 [INFO] AIModel: Model path: D:\MythoMist\mythomist-7b.Q4_K_M.gguf
2025-07-13 19:49:37,962 [INFO] AIModel: Model type: mistral
2025-07-13 19:49:37,963 [INFO] AIModel: GPU layers: 50
2025-07-13 19:49:37,963 [INFO] AIModel: Model file size: 4166.07 MB
2025-07-13 19:49:39,953 [INFO] AIModel: ✅ Model loaded successfully!
2025-07-13 19:49:39,954 [INFO] AIModel: Backend class: <class 'ctransformers.llm.LLM'>
2025-07-13 19:49:39,954 [INFO] AIModel: Memory system ready with 11 existing memories
2025-07-13 19:49:39,954 [INFO] AIModel: ✅ AI Model fully initialized
2025-07-13 19:49:39,954 [WARNING] discord.client: PyNaCl is not installed, voice will NOT be supported
2025-07-13 19:49:39,955 [DEBUG] discord.client: on_ready has successfully been registered as an event
2025-07-13 19:49:39,955 [DEBUG] discord.client: on_message has successfully been registered as an event
2025-07-13 19:49:39,957 [DEBUG] asyncio: Using proactor: IocpProactor
2025-07-13 19:49:39,957 [INFO] discord.client: logging in using static token
2025-07-13 19:49:42,197 [INFO] discord.gateway: Shard ID None has connected to Gateway (Session ID: e6bc64e6435c6dd4421d7fd39cec2ff4).
2025-07-13 19:49:48,667 [DEBUG] memory: Found 2 similar memories
2025-07-13 19:49:58,408 [DEBUG] memory: Added memory ID 12: User: User (TexasEngineer_Vyke): 
Caelin: I feel s...
2025-07-13 19:50:04,220 [DEBUG] memory: Found 0 similar memories
2025-07-13 19:50:08,971 [DEBUG] memory: Added memory ID 13: User: finally
Caelin: I understand the feeling of ...
2025-07-13 19:50:20,726 [DEBUG] memory: Found 2 similar memories
2025-07-13 19:50:28,770 [DEBUG] memory: Added memory ID 14: User: how are you?
Caelin: I am feeling quite cont...
2025-07-13 19:50:46,419 [DEBUG] memory: Found 2 similar memories
2025-07-13 19:50:53,008 [DEBUG] memory: Added memory ID 15: User: User (პროოკოლი): hi
Caelin: Hello, პროოკოლი....
2025-07-13 19:51:01,891 [DEBUG] memory: Found 3 similar memories
2025-07-13 19:51:09,533 [DEBUG] memory: Saved FAISS index with 10 mappings
2025-07-13 19:51:09,534 [DEBUG] memory: Added memory ID 16: User: Great! I have a request
Caelin: I am always ...
2025-07-13 19:51:16,812 [DEBUG] memory: Found 3 similar memories
2025-07-13 19:51:26,333 [DEBUG] memory: Added memory ID 17: User: I need you to remember something
Caelin: It ...
2025-07-13 19:52:15,702 [DEBUG] memory: Found 3 similar memories
2025-07-13 19:52:25,108 [DEBUG] memory: Added memory ID 18: User: Make sure to send 10 links of random FMHY fu...
2025-07-13 19:52:59,419 [DEBUG] memory: Found 3 similar memories
2025-07-13 19:53:06,187 [DEBUG] memory: Added memory ID 19: User: User (TexasEngineer_Vyke): you are good girl...
2025-07-13 19:53:07,495 [DEBUG] memory: Found 1 similar memories
2025-07-13 19:53:13,881 [DEBUG] memory: Added memory ID 20: User: Okay, open each conversation with labeling y...
2025-07-13 19:53:20,998 [DEBUG] memory: Found 2 similar memories
2025-07-13 19:53:27,316 [DEBUG] memory: Saved FAISS index with 15 mappings
2025-07-13 19:53:27,316 [DEBUG] memory: Added memory ID 21: User: Therapist*
Caelin: I'm always open to discus...
2025-07-13 19:53:39,386 [DEBUG] memory: Found 2 similar memories
2025-07-13 19:53:46,299 [DEBUG] memory: Added memory ID 22: User: ;DDDDDDDDDDDDDDDDDDDDDDD
Caelin: It seems li...
2025-07-13 19:54:28,195 [DEBUG] memory: Found 3 similar memories
2025-07-13 19:54:35,234 [DEBUG] memory: Found 2 similar memories
2025-07-13 19:54:35,277 [ERROR] AIModel: Error during generation: exception: access violation reading 0x0000000000000050
2025-07-13 19:54:42,285 [DEBUG] memory: Added memory ID 23: User: Explain this emoji
Caelin: This emoji is a s...
2025-07-13 19:54:42,456 [DEBUG] memory: Found 3 similar memories
2025-07-13 19:54:50,304 [DEBUG] memory: Added memory ID 24: User: ugh
Caelin: It seems like you might be feeli...
2025-07-13 19:54:58,063 [DEBUG] memory: Found 2 similar memories
2025-07-13 19:56:22,352 [INFO] AIModel: === AI MODEL INITIALIZATION ===
2025-07-13 19:56:22,353 [INFO] AIModel: Initializing mood manager...
2025-07-13 19:56:22,355 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-13 19:56:22,681 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/config.json HTTP/1.1" 307 0
2025-07-13 19:56:22,725 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/nateraw/bert-base-uncased-emotion/064d252021b51d95cd0547c89c6489100da0dc4c/config.json HTTP/1.1" 200 0
2025-07-13 19:56:23,147 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/model.safetensors HTTP/1.1" 404 0
2025-07-13 19:56:23,152 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-13 19:56:23,418 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion HTTP/1.1" 200 3678
2025-07-13 19:56:23,469 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-13 19:56:23,501 [INFO] AIModel: ✅ Mood manager initialized
2025-07-13 19:56:23,501 [INFO] AIModel: Initializing memory system...
2025-07-13 19:56:23,502 [INFO] sentence_transformers.SentenceTransformer: Use pytorch device_name: cpu
2025-07-13 19:56:23,502 [INFO] sentence_transformers.SentenceTransformer: Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-13 19:56:23,621 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/main HTTP/1.1" 200 2736
2025-07-13 19:56:23,686 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-13 19:56:23,728 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-13 19:56:23,806 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/discussions?p=0 HTTP/1.1" 200 1434
2025-07-13 19:56:23,908 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-13 19:56:23,949 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-13 19:56:24,006 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/refs%2Fpr%2F1 HTTP/1.1" 200 3701
2025-07-13 19:56:24,130 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-13 19:56:24,173 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-13 19:56:24,190 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors.index.json HTTP/1.1" 404 0
2025-07-13 19:56:24,354 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/README.md HTTP/1.1" 307 0
2025-07-13 19:56:24,373 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors HTTP/1.1" 302 0
2025-07-13 19:56:24,396 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/README.md HTTP/1.1" 200 0
2025-07-13 19:56:24,581 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-13 19:56:24,625 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-13 19:56:24,812 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/sentence_bert_config.json HTTP/1.1" 307 0
2025-07-13 19:56:24,855 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/sentence_bert_config.json HTTP/1.1" 200 0
2025-07-13 19:56:25,039 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/adapter_config.json HTTP/1.1" 404 0
2025-07-13 19:56:25,228 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config.json HTTP/1.1" 307 0
2025-07-13 19:56:25,270 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config.json HTTP/1.1" 200 0
2025-07-13 19:56:25,773 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/tokenizer_config.json HTTP/1.1" 307 0
2025-07-13 19:56:25,815 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/tokenizer_config.json HTTP/1.1" 200 0
2025-07-13 19:56:25,997 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-13 19:56:26,200 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/1_Pooling/config.json HTTP/1.1" 307 0
2025-07-13 19:56:26,241 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/1_Pooling%2Fconfig.json HTTP/1.1" 200 0
2025-07-13 19:56:26,424 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2 HTTP/1.1" 200 6821
2025-07-13 19:56:26,428 [INFO] memory: Connected to database: memory.db
2025-07-13 19:56:26,428 [DEBUG] memory: Memory table created/verified
2025-07-13 19:56:26,435 [INFO] memory: Loaded FAISS index with 21 vectors
2025-07-13 19:56:26,435 [INFO] memory: SemanticMemory initialized with 24 existing memories
2025-07-13 19:56:26,435 [INFO] AIModel: ✅ Memory system initialized
2025-07-13 19:56:26,435 [INFO] AIModel: Starting model loading...
2025-07-13 19:56:26,435 [INFO] AIModel: === MODEL LOADING DEBUG ===
2025-07-13 19:56:26,435 [INFO] AIModel: Model path: D:\MythoMist\mythomist-7b.Q4_K_M.gguf
2025-07-13 19:56:26,435 [INFO] AIModel: Model type: mistral
2025-07-13 19:56:26,435 [INFO] AIModel: GPU layers: 50
2025-07-13 19:56:26,435 [INFO] AIModel: Model file size: 4166.07 MB
2025-07-13 19:56:28,231 [INFO] AIModel: ✅ Model loaded successfully!
2025-07-13 19:56:28,231 [INFO] AIModel: Backend class: <class 'ctransformers.llm.LLM'>
2025-07-13 19:56:28,232 [INFO] AIModel: Memory system ready with 24 existing memories
2025-07-13 19:56:28,232 [INFO] AIModel: ✅ AI Model fully initialized
2025-07-13 19:56:28,232 [WARNING] discord.client: PyNaCl is not installed, voice will NOT be supported
2025-07-13 19:56:28,233 [DEBUG] discord.client: on_ready has successfully been registered as an event
2025-07-13 19:56:28,233 [DEBUG] discord.client: on_message has successfully been registered as an event
2025-07-13 19:56:28,234 [DEBUG] asyncio: Using proactor: IocpProactor
2025-07-13 19:56:28,234 [INFO] discord.client: logging in using static token
2025-07-13 19:56:29,332 [INFO] discord.gateway: Shard ID None has connected to Gateway (Session ID: 41ab7e1286b9e5f46f57d69dc5a928bc).
2025-07-13 19:57:07,843 [DEBUG] memory: Found 3 similar memories
2025-07-13 19:57:19,042 [DEBUG] memory: Added memory ID 25: User: User (TexasEngineer_Vyke): wakey
Caelin: Hel...
2025-07-13 19:57:35,852 [DEBUG] memory: Found 3 similar memories
2025-07-13 19:57:45,954 [DEBUG] memory: Added memory ID 26: User: what did i tell about TexasEngineer thing
Ca...
2025-07-13 19:58:01,940 [DEBUG] memory: Found 3 similar memories
2025-07-13 19:58:13,974 [DEBUG] memory: Added memory ID 27: User: yep pls dont do it again
Caelin: I understan...
2025-07-13 20:08:54,361 [DEBUG] memory: Found 3 similar memories
2025-07-13 20:09:03,010 [DEBUG] memory: Added memory ID 28: User: User (პროოკოლი): hi
Caelin: Hello, პროოკოლი....
2025-07-13 20:09:12,518 [DEBUG] memory: Found 3 similar memories
2025-07-13 20:09:26,126 [DEBUG] memory: Saved FAISS index with 20 mappings
2025-07-13 20:09:26,126 [DEBUG] memory: Added memory ID 29: User: Great, i have another request
Caelin: I am h...
2025-07-13 20:09:47,765 [DEBUG] memory: Found 2 similar memories
2025-07-13 20:09:52,369 [DEBUG] memory: Found 3 similar memories
2025-07-13 20:09:52,588 [ERROR] AIModel: Error during generation: exception: access violation reading 0x0000000000000000
2025-07-13 20:10:01,064 [DEBUG] memory: Added memory ID 30: User: User (Kuka): im gonna fuck you
Caelin: I hav...
2025-07-13 20:10:12,723 [DEBUG] memory: Found 2 similar memories
2025-07-13 20:10:20,384 [DEBUG] memory: Found 2 similar memories
2025-07-13 23:58:34,051 [INFO] AIModel: === AI MODEL INITIALIZATION ===
2025-07-13 23:58:34,053 [INFO] AIModel: Initializing mood manager...
2025-07-13 23:58:34,144 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-13 23:58:35,576 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/config.json HTTP/1.1" 307 0
2025-07-13 23:58:35,615 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/nateraw/bert-base-uncased-emotion/064d252021b51d95cd0547c89c6489100da0dc4c/config.json HTTP/1.1" 200 0
2025-07-13 23:58:37,100 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/model.safetensors HTTP/1.1" 404 0
2025-07-13 23:58:37,103 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-13 23:58:37,360 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion HTTP/1.1" 200 3678
2025-07-13 23:58:37,888 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/main HTTP/1.1" 200 2736
2025-07-13 23:58:38,358 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/discussions?p=0 HTTP/1.1" 200 1434
2025-07-13 23:58:38,505 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-13 23:58:38,558 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/refs%2Fpr%2F1 HTTP/1.1" 200 3701
2025-07-13 23:58:38,560 [INFO] AIModel: ✅ Mood manager initialized
2025-07-13 23:58:38,560 [INFO] AIModel: Initializing memory system...
2025-07-13 23:58:38,561 [INFO] sentence_transformers.SentenceTransformer: Use pytorch device_name: cpu
2025-07-13 23:58:38,562 [INFO] sentence_transformers.SentenceTransformer: Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-13 23:58:38,739 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors.index.json HTTP/1.1" 404 0
2025-07-13 23:58:38,739 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-13 23:58:38,779 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-13 23:58:38,924 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors HTTP/1.1" 302 0
2025-07-13 23:58:38,974 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-13 23:58:39,014 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-13 23:58:39,201 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-13 23:58:39,244 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-13 23:58:39,421 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/README.md HTTP/1.1" 307 0
2025-07-13 23:58:39,461 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/README.md HTTP/1.1" 200 0
2025-07-13 23:58:39,651 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-13 23:58:39,692 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-13 23:58:39,877 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/sentence_bert_config.json HTTP/1.1" 307 0
2025-07-13 23:58:39,919 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/sentence_bert_config.json HTTP/1.1" 200 0
2025-07-13 23:58:40,114 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/adapter_config.json HTTP/1.1" 404 0
2025-07-13 23:58:40,297 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config.json HTTP/1.1" 307 0
2025-07-13 23:58:40,338 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config.json HTTP/1.1" 200 0
2025-07-13 23:58:42,865 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/tokenizer_config.json HTTP/1.1" 307 0
2025-07-13 23:58:42,905 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/tokenizer_config.json HTTP/1.1" 200 0
2025-07-13 23:58:43,098 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-13 23:58:43,333 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/1_Pooling/config.json HTTP/1.1" 307 0
2025-07-13 23:58:43,374 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/1_Pooling%2Fconfig.json HTTP/1.1" 200 0
2025-07-13 23:58:43,565 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2 HTTP/1.1" 200 6821
2025-07-13 23:58:43,580 [INFO] memory: Connected to database: memory.db
2025-07-13 23:58:43,588 [DEBUG] memory: Memory table created/verified
2025-07-13 23:58:43,597 [INFO] memory: Loaded FAISS index with 26 vectors
2025-07-13 23:58:43,598 [INFO] memory: SemanticMemory initialized with 30 existing memories
2025-07-13 23:58:43,598 [INFO] AIModel: ✅ Memory system initialized
2025-07-13 23:58:43,598 [INFO] AIModel: Starting model loading...
2025-07-13 23:58:43,598 [INFO] AIModel: === MODEL LOADING DEBUG ===
2025-07-13 23:58:43,599 [INFO] AIModel: Model path: D:\MythoMist\mythomist-7b.Q4_K_M.gguf
2025-07-13 23:58:43,599 [INFO] AIModel: Model type: mistral
2025-07-13 23:58:43,599 [INFO] AIModel: GPU layers: 50
2025-07-13 23:58:43,599 [INFO] AIModel: Model file size: 4166.07 MB
2025-07-13 23:58:48,292 [INFO] AIModel: ✅ Model loaded successfully!
2025-07-13 23:58:48,293 [INFO] AIModel: Backend class: <class 'ctransformers.llm.LLM'>
2025-07-13 23:58:48,293 [INFO] AIModel: Memory system ready with 30 existing memories
2025-07-13 23:58:48,293 [INFO] AIModel: ✅ AI Model fully initialized
2025-07-13 23:58:48,294 [WARNING] discord.client: PyNaCl is not installed, voice will NOT be supported
2025-07-13 23:58:48,294 [DEBUG] discord.client: on_ready has successfully been registered as an event
2025-07-13 23:58:48,295 [DEBUG] discord.client: on_message has successfully been registered as an event
2025-07-13 23:58:48,295 [DEBUG] asyncio: Using proactor: IocpProactor
2025-07-13 23:58:48,297 [INFO] discord.client: logging in using static token
2025-07-13 23:58:50,544 [INFO] discord.gateway: Shard ID None has connected to Gateway (Session ID: cbf747273a7d5b9446fa96be188213c3).
2025-07-13 23:59:14,966 [DEBUG] memory: Found 2 similar memories
2025-07-13 23:59:22,766 [DEBUG] memory: Added memory ID 31: User: User (TexasEngineer_Vyke): 
Caelin: Hello, T...
2025-07-13 23:59:39,133 [DEBUG] memory: Found 2 similar memories
2025-07-13 23:59:47,072 [DEBUG] memory: Added memory ID 32: User: you are texasengineer not me u gayass
Caelin...
2025-07-14 00:10:29,097 [ERROR] memory: Error saving FAISS index: name 'open' is not defined
2025-07-14 00:10:29,097 [INFO] memory: Semantic memory system closed
2025-07-14 00:10:34,935 [INFO] AIModel: === AI MODEL INITIALIZATION ===
2025-07-14 00:10:34,935 [INFO] AIModel: Initializing mood manager...
2025-07-14 00:10:34,937 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-14 00:10:35,315 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/config.json HTTP/1.1" 307 0
2025-07-14 00:10:35,355 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/nateraw/bert-base-uncased-emotion/064d252021b51d95cd0547c89c6489100da0dc4c/config.json HTTP/1.1" 200 0
2025-07-14 00:10:35,874 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/model.safetensors HTTP/1.1" 404 0
2025-07-14 00:10:35,879 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-14 00:10:36,133 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion HTTP/1.1" 200 3678
2025-07-14 00:10:36,217 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-14 00:10:36,248 [INFO] AIModel: ✅ Mood manager initialized
2025-07-14 00:10:36,248 [INFO] AIModel: Initializing memory system...
2025-07-14 00:10:36,249 [INFO] sentence_transformers.SentenceTransformer: Use pytorch device_name: cpu
2025-07-14 00:10:36,249 [INFO] sentence_transformers.SentenceTransformer: Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-14 00:10:36,331 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/main HTTP/1.1" 200 2736
2025-07-14 00:10:36,434 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-14 00:10:36,477 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-14 00:10:36,677 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-14 00:10:36,718 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-14 00:10:36,801 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/discussions?p=0 HTTP/1.1" 200 1434
2025-07-14 00:10:36,902 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-14 00:10:36,945 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-14 00:10:37,003 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/refs%2Fpr%2F1 HTTP/1.1" 200 3701
2025-07-14 00:10:37,130 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/README.md HTTP/1.1" 307 0
2025-07-14 00:10:37,172 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/README.md HTTP/1.1" 200 0
2025-07-14 00:10:37,184 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors.index.json HTTP/1.1" 404 0
2025-07-14 00:10:37,353 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-14 00:10:37,367 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors HTTP/1.1" 302 0
2025-07-14 00:10:37,395 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-14 00:10:37,572 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/sentence_bert_config.json HTTP/1.1" 307 0
2025-07-14 00:10:37,612 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/sentence_bert_config.json HTTP/1.1" 200 0
2025-07-14 00:10:37,795 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/adapter_config.json HTTP/1.1" 404 0
2025-07-14 00:10:37,982 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config.json HTTP/1.1" 307 0
2025-07-14 00:10:38,024 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config.json HTTP/1.1" 200 0
2025-07-14 00:10:38,504 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/tokenizer_config.json HTTP/1.1" 307 0
2025-07-14 00:10:38,546 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/tokenizer_config.json HTTP/1.1" 200 0
2025-07-14 00:10:38,733 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-14 00:10:38,948 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/1_Pooling/config.json HTTP/1.1" 307 0
2025-07-14 00:10:38,989 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/1_Pooling%2Fconfig.json HTTP/1.1" 200 0
2025-07-14 00:10:39,177 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2 HTTP/1.1" 200 6821
2025-07-14 00:10:39,179 [INFO] memory: Connected to database: memory.db
2025-07-14 00:10:39,180 [DEBUG] memory: Memory table created/verified
2025-07-14 00:10:39,188 [INFO] memory: Loaded FAISS index with 28 vectors
2025-07-14 00:10:39,189 [INFO] memory: SemanticMemory initialized with 32 existing memories
2025-07-14 00:10:39,189 [INFO] AIModel: ✅ Memory system initialized
2025-07-14 00:10:39,189 [INFO] AIModel: Starting model loading...
2025-07-14 00:10:39,189 [INFO] AIModel: === MODEL LOADING DEBUG ===
2025-07-14 00:10:39,189 [INFO] AIModel: Model path: D:\MythoMist\mythomist-7b.Q4_K_M.gguf
2025-07-14 00:10:39,189 [INFO] AIModel: Model type: mistral
2025-07-14 00:10:39,189 [INFO] AIModel: GPU layers: 50
2025-07-14 00:10:39,189 [INFO] AIModel: Model file size: 4166.07 MB
2025-07-14 00:10:40,981 [INFO] AIModel: ✅ Model loaded successfully!
2025-07-14 00:10:40,981 [INFO] AIModel: Backend class: <class 'ctransformers.llm.LLM'>
2025-07-14 00:10:40,982 [INFO] AIModel: Memory system ready with 32 existing memories
2025-07-14 00:10:40,982 [INFO] AIModel: ✅ AI Model fully initialized
2025-07-14 00:10:40,982 [WARNING] discord.client: PyNaCl is not installed, voice will NOT be supported
2025-07-14 00:10:40,982 [DEBUG] discord.client: on_ready has successfully been registered as an event
2025-07-14 00:10:40,984 [DEBUG] discord.client: on_message has successfully been registered as an event
2025-07-14 00:10:40,984 [DEBUG] asyncio: Using proactor: IocpProactor
2025-07-14 00:10:40,985 [INFO] discord.client: logging in using static token
2025-07-14 00:10:43,230 [INFO] discord.gateway: Shard ID None has connected to Gateway (Session ID: 92e44f156d17b24ef1f2d82e228e7fd7).
2025-07-14 00:13:06,833 [INFO] AIModel: === AI MODEL INITIALIZATION ===
2025-07-14 00:13:06,833 [INFO] AIModel: Initializing mood manager...
2025-07-14 00:13:06,835 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-14 00:13:07,374 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/config.json HTTP/1.1" 307 0
2025-07-14 00:13:07,413 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/nateraw/bert-base-uncased-emotion/064d252021b51d95cd0547c89c6489100da0dc4c/config.json HTTP/1.1" 200 0
2025-07-14 00:13:08,131 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/model.safetensors HTTP/1.1" 404 0
2025-07-14 00:13:08,135 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-14 00:13:08,463 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-14 00:13:08,494 [INFO] AIModel: ✅ Mood manager initialized
2025-07-14 00:13:08,494 [INFO] AIModel: Initializing memory system...
2025-07-14 00:13:08,494 [INFO] sentence_transformers.SentenceTransformer: Use pytorch device_name: cpu
2025-07-14 00:13:08,494 [INFO] sentence_transformers.SentenceTransformer: Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-14 00:13:08,652 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion HTTP/1.1" 200 3678
2025-07-14 00:13:08,675 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-14 00:13:08,713 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-14 00:13:08,867 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/main HTTP/1.1" 200 2736
2025-07-14 00:13:08,890 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-14 00:13:08,930 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-14 00:13:09,053 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/discussions?p=0 HTTP/1.1" 200 1434
2025-07-14 00:13:09,108 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-14 00:13:09,148 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-14 00:13:09,327 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/README.md HTTP/1.1" 307 0
2025-07-14 00:13:09,367 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/README.md HTTP/1.1" 200 0
2025-07-14 00:13:09,507 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/refs%2Fpr%2F1 HTTP/1.1" 200 3701
2025-07-14 00:13:09,548 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-14 00:13:09,588 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-14 00:13:09,684 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors.index.json HTTP/1.1" 404 0
2025-07-14 00:13:09,766 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/sentence_bert_config.json HTTP/1.1" 307 0
2025-07-14 00:13:09,806 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/sentence_bert_config.json HTTP/1.1" 200 0
2025-07-14 00:13:09,864 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors HTTP/1.1" 302 0
2025-07-14 00:13:09,988 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/adapter_config.json HTTP/1.1" 404 0
2025-07-14 00:13:10,167 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config.json HTTP/1.1" 307 0
2025-07-14 00:13:10,207 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config.json HTTP/1.1" 200 0
2025-07-14 00:13:10,727 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/tokenizer_config.json HTTP/1.1" 307 0
2025-07-14 00:13:10,766 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/tokenizer_config.json HTTP/1.1" 200 0
2025-07-14 00:13:10,944 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-14 00:13:11,147 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/1_Pooling/config.json HTTP/1.1" 307 0
2025-07-14 00:13:11,186 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/1_Pooling%2Fconfig.json HTTP/1.1" 200 0
2025-07-14 00:13:11,375 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2 HTTP/1.1" 200 6821
2025-07-14 00:13:11,377 [INFO] memory: Connected to database: memory.db
2025-07-14 00:13:11,377 [DEBUG] memory: Memory table created/verified
2025-07-14 00:13:11,378 [INFO] memory: Loaded FAISS index with 28 vectors
2025-07-14 00:13:11,378 [INFO] memory: SemanticMemory initialized with 32 existing memories
2025-07-14 00:13:11,378 [INFO] AIModel: ✅ Memory system initialized
2025-07-14 00:13:11,378 [INFO] AIModel: Starting model loading...
2025-07-14 00:13:11,378 [INFO] AIModel: === MODEL LOADING DEBUG ===
2025-07-14 00:13:11,378 [INFO] AIModel: Model path: D:\MythoMist\mythomist-7b.Q4_K_M.gguf
2025-07-14 00:13:11,378 [INFO] AIModel: Model type: mistral
2025-07-14 00:13:11,378 [INFO] AIModel: GPU layers: 50
2025-07-14 00:13:11,378 [INFO] AIModel: Model file size: 4166.07 MB
2025-07-14 00:13:13,275 [INFO] AIModel: ✅ Model loaded successfully!
2025-07-14 00:13:13,275 [INFO] AIModel: Backend class: <class 'ctransformers.llm.LLM'>
2025-07-14 00:13:13,275 [INFO] AIModel: Memory system ready with 32 existing memories
2025-07-14 00:13:13,275 [INFO] AIModel: ✅ AI Model fully initialized
2025-07-14 00:13:13,276 [WARNING] discord.client: PyNaCl is not installed, voice will NOT be supported
2025-07-14 00:13:13,277 [DEBUG] discord.client: on_ready has successfully been registered as an event
2025-07-14 00:13:13,277 [DEBUG] discord.client: on_message has successfully been registered as an event
2025-07-14 00:13:13,278 [DEBUG] asyncio: Using proactor: IocpProactor
2025-07-14 00:13:13,279 [INFO] discord.client: logging in using static token
2025-07-14 00:13:14,510 [INFO] discord.gateway: Shard ID None has connected to Gateway (Session ID: 41627d66e789425abb0dda18014a8761).
2025-07-14 00:15:08,715 [DEBUG] memory: Found 2 similar memories
2025-07-14 00:15:16,204 [DEBUG] memory: Added memory ID 33: User: User (TexasEngineer_Vyke): 
Caelin: Hello, T...
2025-07-14 00:27:10,192 [ERROR] memory: Error saving FAISS index: name 'open' is not defined
2025-07-14 00:27:10,193 [INFO] memory: Semantic memory system closed
2025-07-14 00:27:16,186 [INFO] AIModel: === AI MODEL INITIALIZATION ===
2025-07-14 00:27:16,186 [INFO] AIModel: Initializing mood manager...
2025-07-14 00:27:16,187 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-14 00:27:16,750 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/config.json HTTP/1.1" 307 0
2025-07-14 00:27:16,791 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/nateraw/bert-base-uncased-emotion/064d252021b51d95cd0547c89c6489100da0dc4c/config.json HTTP/1.1" 200 0
2025-07-14 00:27:17,156 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/model.safetensors HTTP/1.1" 404 0
2025-07-14 00:27:17,159 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-14 00:27:17,437 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion HTTP/1.1" 200 3678
2025-07-14 00:27:17,512 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-14 00:27:17,544 [INFO] AIModel: ✅ Mood manager initialized
2025-07-14 00:27:17,544 [INFO] AIModel: Initializing memory system...
2025-07-14 00:27:17,544 [INFO] sentence_transformers.SentenceTransformer: Use pytorch device_name: cpu
2025-07-14 00:27:17,545 [INFO] sentence_transformers.SentenceTransformer: Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-14 00:27:17,727 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-14 00:27:17,767 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-14 00:27:17,945 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-14 00:27:17,985 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-14 00:27:18,082 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/main HTTP/1.1" 200 2736
2025-07-14 00:27:18,165 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-14 00:27:18,207 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-14 00:27:18,281 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/discussions?p=0 HTTP/1.1" 200 1434
2025-07-14 00:27:18,394 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/README.md HTTP/1.1" 307 0
2025-07-14 00:27:18,435 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/README.md HTTP/1.1" 200 0
2025-07-14 00:27:18,481 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/refs%2Fpr%2F1 HTTP/1.1" 200 3701
2025-07-14 00:27:18,617 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-14 00:27:18,657 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-14 00:27:18,671 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors.index.json HTTP/1.1" 404 0
2025-07-14 00:27:18,838 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/sentence_bert_config.json HTTP/1.1" 307 0
2025-07-14 00:27:18,862 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors HTTP/1.1" 302 0
2025-07-14 00:27:18,879 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/sentence_bert_config.json HTTP/1.1" 200 0
2025-07-14 00:27:19,311 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/adapter_config.json HTTP/1.1" 404 0
2025-07-14 00:27:19,497 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config.json HTTP/1.1" 307 0
2025-07-14 00:27:19,537 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config.json HTTP/1.1" 200 0
2025-07-14 00:27:20,052 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/tokenizer_config.json HTTP/1.1" 307 0
2025-07-14 00:27:20,092 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/tokenizer_config.json HTTP/1.1" 200 0
2025-07-14 00:27:20,278 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-14 00:27:20,481 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/1_Pooling/config.json HTTP/1.1" 307 0
2025-07-14 00:27:20,522 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/1_Pooling%2Fconfig.json HTTP/1.1" 200 0
2025-07-14 00:27:20,704 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2 HTTP/1.1" 200 6821
2025-07-14 00:27:20,707 [INFO] memory: Connected to database: memory.db
2025-07-14 00:27:20,708 [DEBUG] memory: Memory table created/verified
2025-07-14 00:27:20,708 [INFO] memory: Loaded FAISS index with 29 vectors
2025-07-14 00:27:20,708 [INFO] memory: SemanticMemory initialized with 33 existing memories
2025-07-14 00:27:20,708 [INFO] AIModel: ✅ Memory system initialized
2025-07-14 00:27:20,708 [INFO] AIModel: Starting model loading...
2025-07-14 00:27:20,709 [INFO] AIModel: === MODEL LOADING DEBUG ===
2025-07-14 00:27:20,709 [INFO] AIModel: Model path: D:\MythoMist\mythomist-7b.Q4_K_M.gguf
2025-07-14 00:27:20,709 [INFO] AIModel: Model type: mistral
2025-07-14 00:27:20,709 [INFO] AIModel: GPU layers: 50
2025-07-14 00:27:20,709 [INFO] AIModel: Model file size: 4166.07 MB
2025-07-14 00:27:22,609 [INFO] AIModel: ✅ Model loaded successfully!
2025-07-14 00:27:22,610 [INFO] AIModel: Backend class: <class 'ctransformers.llm.LLM'>
2025-07-14 00:27:22,610 [INFO] AIModel: Memory system ready with 33 existing memories
2025-07-14 00:27:22,610 [INFO] AIModel: ✅ AI Model fully initialized
2025-07-14 00:27:22,611 [WARNING] discord.client: PyNaCl is not installed, voice will NOT be supported
2025-07-14 00:27:22,611 [DEBUG] discord.client: on_ready has successfully been registered as an event
2025-07-14 00:27:22,612 [DEBUG] discord.client: on_message has successfully been registered as an event
2025-07-14 00:27:22,613 [DEBUG] discord.client: on_disconnect has successfully been registered as an event
2025-07-14 00:27:22,613 [DEBUG] asyncio: Using proactor: IocpProactor
2025-07-14 00:27:22,613 [INFO] discord.client: logging in using static token
2025-07-14 00:27:24,853 [INFO] discord.gateway: Shard ID None has connected to Gateway (Session ID: 267d96c75b98191c1f72c5f833aab64e).
2025-07-14 00:27:33,050 [INFO] ProactiveEngine: [Proactive] User input updated: 'TexasEngineer_Vyke: hey caelin...'
2025-07-14 00:27:33,398 [DEBUG] memory: Found 0 similar memories
2025-07-14 00:27:39,026 [DEBUG] memory: Added memory ID 34: User: User (TexasEngineer_Vyke): hey
Caelin: Hello...
2025-07-14 00:31:11,883 [INFO] ProactiveEngine: [Proactive] User input updated: '*observing the quiet space*...'
2025-07-14 00:31:11,884 [INFO] ProactiveEngine: [Proactive] Checking engagement (attempt #1)
2025-07-14 00:31:11,885 [INFO] ProactiveEngine: [Proactive] Cooldown time: 1752438671.9s, threshold: 300s, on_cooldown: False
2025-07-14 00:31:11,885 [INFO] ProactiveEngine: [Proactive] Current state:
2025-07-14 00:31:11,885 [INFO] ProactiveEngine:   - Mood: joy (intensity: 0.51)
2025-07-14 00:31:11,886 [INFO] ProactiveEngine:   - Time since last: 0.0s
2025-07-14 00:31:11,886 [INFO] ProactiveEngine:   - Last input length: 27 chars
2025-07-14 00:31:11,886 [INFO] ProactiveEngine:   - Neutral level: 0.00
2025-07-14 00:31:11,886 [INFO] ProactiveEngine: [Proactive] ✅ Engaging due to strong joy emotion
2025-07-14 00:31:11,886 [INFO] ProactiveEngine: [Proactive] Generating message (attempt #1)
2025-07-14 00:31:11,934 [INFO] ProactiveEngine: [Proactive] Sending to model...
2025-07-14 00:31:17,457 [INFO] ProactiveEngine: [Proactive] Generated: 'In this moment of tranquility, I can't help but fe...'
2025-07-14 00:31:37,725 [INFO] ProactiveEngine: [Proactive] User input updated: 'TexasEngineer_Vyke: cmnn girl ...'
2025-07-14 00:31:38,046 [DEBUG] memory: Found 0 similar memories
2025-07-14 00:31:43,980 [DEBUG] memory: Added memory ID 35: User: TexasEngineer_Vyke: cmnn girl it was so rand...
2025-07-14 00:34:56,894 [INFO] ProactiveEngine: [Proactive] User input updated: '*observing the quiet space*...'
2025-07-14 00:34:56,895 [INFO] ProactiveEngine: [Proactive] Checking engagement (attempt #2)
2025-07-14 00:34:56,896 [INFO] ProactiveEngine: [Proactive] Cooldown time: 225.0s, threshold: 300s, on_cooldown: True
2025-07-14 00:34:56,896 [INFO] ProactiveEngine: [Proactive] ❌ On cooldown, skipping
2025-07-14 00:35:41,878 [INFO] ProactiveEngine: [Proactive] User input updated: '*observing the quiet space*...'
2025-07-14 00:35:41,880 [INFO] ProactiveEngine: [Proactive] Checking engagement (attempt #3)
2025-07-14 00:35:41,881 [INFO] ProactiveEngine: [Proactive] Cooldown time: 269.9s, threshold: 300s, on_cooldown: True
2025-07-14 00:35:41,882 [INFO] ProactiveEngine: [Proactive] ❌ On cooldown, skipping
2025-07-14 00:36:26,878 [INFO] ProactiveEngine: [Proactive] User input updated: '*observing the quiet space*...'
2025-07-14 00:36:26,879 [INFO] ProactiveEngine: [Proactive] Checking engagement (attempt #4)
2025-07-14 00:36:26,879 [INFO] ProactiveEngine: [Proactive] Cooldown time: 314.9s, threshold: 300s, on_cooldown: False
2025-07-14 00:36:26,879 [INFO] ProactiveEngine: [Proactive] Current state:
2025-07-14 00:36:26,879 [INFO] ProactiveEngine:   - Mood: fear (intensity: 0.37)
2025-07-14 00:36:26,879 [INFO] ProactiveEngine:   - Time since last: 0.0s
2025-07-14 00:36:26,879 [INFO] ProactiveEngine:   - Last input length: 27 chars
2025-07-14 00:36:26,879 [INFO] ProactiveEngine:   - Neutral level: 0.00
2025-07-14 00:36:26,879 [INFO] ProactiveEngine: [Proactive] ✅ Engaging due to strong fear emotion
2025-07-14 00:36:26,879 [INFO] ProactiveEngine: [Proactive] Generating message (attempt #4)
2025-07-14 00:36:26,914 [INFO] ProactiveEngine: [Proactive] Sending to model...
2025-07-14 00:36:32,190 [INFO] ProactiveEngine: [Proactive] Generated: 'In this moment of serenity, I'm reminded of how im...'
2025-07-14 00:39:26,881 [INFO] ProactiveEngine: [Proactive] User input updated: '*observing the quiet space*...'
2025-07-14 00:39:26,882 [INFO] ProactiveEngine: [Proactive] Checking engagement (attempt #5)
2025-07-14 00:39:26,883 [INFO] ProactiveEngine: [Proactive] Cooldown time: 180.0s, threshold: 300s, on_cooldown: True
2025-07-14 00:39:26,884 [INFO] ProactiveEngine: [Proactive] ❌ On cooldown, skipping
2025-07-14 00:40:11,874 [INFO] ProactiveEngine: [Proactive] User input updated: '*observing the quiet space*...'
2025-07-14 00:40:11,874 [INFO] ProactiveEngine: [Proactive] Checking engagement (attempt #6)
2025-07-14 00:40:11,874 [INFO] ProactiveEngine: [Proactive] Cooldown time: 225.0s, threshold: 300s, on_cooldown: True
2025-07-14 00:40:11,874 [INFO] ProactiveEngine: [Proactive] ❌ On cooldown, skipping
2025-07-14 00:40:56,892 [INFO] ProactiveEngine: [Proactive] User input updated: '*observing the quiet space*...'
2025-07-14 00:40:56,892 [INFO] ProactiveEngine: [Proactive] Checking engagement (attempt #7)
2025-07-14 00:40:56,892 [INFO] ProactiveEngine: [Proactive] Cooldown time: 270.0s, threshold: 300s, on_cooldown: True
2025-07-14 00:40:56,892 [INFO] ProactiveEngine: [Proactive] ❌ On cooldown, skipping
2025-07-14 00:41:26,207 [ERROR] memory: Error saving FAISS index: name 'open' is not defined
2025-07-14 00:41:26,209 [INFO] memory: Semantic memory system closed
2025-07-14 00:41:38,113 [INFO] AIModel: === AI MODEL INITIALIZATION ===
2025-07-14 00:41:38,113 [INFO] AIModel: Initializing mood manager...
2025-07-14 00:41:38,115 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-14 00:41:39,441 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/config.json HTTP/1.1" 307 0
2025-07-14 00:41:39,482 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/nateraw/bert-base-uncased-emotion/064d252021b51d95cd0547c89c6489100da0dc4c/config.json HTTP/1.1" 200 0
2025-07-14 00:41:40,213 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/main/model.safetensors HTTP/1.1" 404 0
2025-07-14 00:41:40,218 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): huggingface.co:443
2025-07-14 00:41:40,480 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion HTTP/1.1" 200 3676
2025-07-14 00:41:40,631 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-14 00:41:40,677 [INFO] AIModel: ✅ Mood manager initialized
2025-07-14 00:41:40,677 [INFO] AIModel: Initializing memory system...
2025-07-14 00:41:40,677 [INFO] sentence_transformers.SentenceTransformer: Use pytorch device_name: cpu
2025-07-14 00:41:40,677 [INFO] sentence_transformers.SentenceTransformer: Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-14 00:41:40,683 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/main HTTP/1.1" 200 2736
2025-07-14 00:41:40,859 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-14 00:41:40,866 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/discussions?p=0 HTTP/1.1" 200 1434
2025-07-14 00:41:40,898 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-14 00:41:41,068 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/nateraw/bert-base-uncased-emotion/commits/refs%2Fpr%2F1 HTTP/1.1" 200 3701
2025-07-14 00:41:41,086 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-14 00:41:41,126 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-14 00:41:41,277 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors.index.json HTTP/1.1" 404 0
2025-07-14 00:41:41,309 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config_sentence_transformers.json HTTP/1.1" 307 0
2025-07-14 00:41:41,349 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config_sentence_transformers.json HTTP/1.1" 200 0
2025-07-14 00:41:41,631 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/README.md HTTP/1.1" 307 0
2025-07-14 00:41:41,671 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/README.md HTTP/1.1" 200 0
2025-07-14 00:41:41,747 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /nateraw/bert-base-uncased-emotion/resolve/refs%2Fpr%2F1/model.safetensors HTTP/1.1" 302 0
2025-07-14 00:41:41,850 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/modules.json HTTP/1.1" 307 0
2025-07-14 00:41:41,890 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/modules.json HTTP/1.1" 200 0
2025-07-14 00:41:42,084 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/sentence_bert_config.json HTTP/1.1" 307 0
2025-07-14 00:41:42,123 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/sentence_bert_config.json HTTP/1.1" 200 0
2025-07-14 00:41:42,309 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/adapter_config.json HTTP/1.1" 404 0
2025-07-14 00:41:42,502 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/config.json HTTP/1.1" 307 0
2025-07-14 00:41:42,541 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/config.json HTTP/1.1" 200 0
2025-07-14 00:41:43,237 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/tokenizer_config.json HTTP/1.1" 307 0
2025-07-14 00:41:43,279 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/tokenizer_config.json HTTP/1.1" 200 0
2025-07-14 00:41:43,461 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2/tree/main/additional_chat_templates?recursive=False&expand=False HTTP/1.1" 404 64
2025-07-14 00:41:43,665 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /sentence-transformers/all-MiniLM-L6-v2/resolve/main/1_Pooling/config.json HTTP/1.1" 307 0
2025-07-14 00:41:43,705 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "HEAD /api/resolve-cache/models/sentence-transformers/all-MiniLM-L6-v2/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/1_Pooling%2Fconfig.json HTTP/1.1" 200 0
2025-07-14 00:41:43,932 [DEBUG] urllib3.connectionpool: https://huggingface.co:443 "GET /api/models/sentence-transformers/all-MiniLM-L6-v2 HTTP/1.1" 200 6821
2025-07-14 00:41:43,933 [INFO] memory: Connected to database: memory.db
2025-07-14 00:41:43,934 [DEBUG] memory: Memory table created/verified
2025-07-14 00:41:43,934 [INFO] memory: Loaded FAISS index with 31 vectors
2025-07-14 00:41:43,934 [INFO] memory: SemanticMemory initialized with 35 existing memories
2025-07-14 00:41:43,934 [INFO] AIModel: ✅ Memory system initialized
2025-07-14 00:41:43,935 [INFO] AIModel: Starting model loading...
2025-07-14 00:41:43,935 [INFO] AIModel: === MODEL LOADING DEBUG ===
2025-07-14 00:41:43,935 [INFO] AIModel: Model path: D:\MythoMist\mythomist-7b.Q4_K_M.gguf
2025-07-14 00:41:43,935 [INFO] AIModel: Model type: mistral
2025-07-14 00:41:43,935 [INFO] AIModel: GPU layers: 50
2025-07-14 00:41:43,935 [INFO] AIModel: Model file size: 4166.07 MB
2025-07-14 00:41:43,935 [ERROR] AIModel: ❌ Failed to load model: TypeError: 'use_mlock' is an invalid keyword argument for from_pretrained()
2025-07-14 00:41:43,935 [INFO] AIModel: Trying fallback with fewer GPU layers...
2025-07-14 00:41:45,349 [INFO] AIModel: ✅ Model loaded with reduced GPU layers!
2025-07-14 00:41:45,349 [INFO] AIModel: ✅ AI Model fully initialized
2025-07-14 00:41:45,349 [WARNING] discord.client: PyNaCl is not installed, voice will NOT be supported
2025-07-14 00:41:45,350 [DEBUG] discord.client: on_ready has successfully been registered as an event
2025-07-14 00:41:45,351 [DEBUG] discord.client: on_message has successfully been registered as an event
2025-07-14 00:41:45,352 [DEBUG] discord.client: on_disconnect has successfully been registered as an event
2025-07-14 00:41:45,352 [DEBUG] asyncio: Using proactor: IocpProactor
2025-07-14 00:41:45,353 [INFO] discord.client: logging in using static token
2025-07-14 00:41:47,715 [INFO] discord.gateway: Shard ID None has connected to Gateway (Session ID: 68d0c040cde4b2aa416189ab7d8d4999).
2025-07-14 00:41:49,731 [INFO] WebExplorer: [WebExplorer] Caelin is browsing the internet...
2025-07-14 00:41:49,731 [INFO] WebExplorer: [WebExplorer] Visiting https://www.reddit.com/r/science/hot.json
2025-07-14 00:41:49,732 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): www.reddit.com:443
2025-07-14 00:41:50,412 [DEBUG] urllib3.connectionpool: https://www.reddit.com:443 "GET /r/science/hot.json HTTP/1.1" 200 15513
2025-07-14 00:41:50,421 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): www.scimex.org:443
2025-07-14 00:41:53,147 [DEBUG] urllib3.connectionpool: https://www.scimex.org:443 "GET /newsfeed/artificial-sweeteners-leave-bitter-aftertaste-for-the-environment HTTP/1.1" 200 None
2025-07-14 00:41:53,251 [DEBUG] memory: Added memory ID 36: Web Discovery: Artificial sweeteners, widely used ...
2025-07-14 00:41:53,251 [INFO] WebExplorer: [Learning] Stored discovery in memory ID 36
2025-07-14 00:41:53,251 [INFO] WebExplorer: [WebExplorer] Found interesting: Artificial sweeteners, widely used in soft drinks,...
2025-07-14 00:42:34,740 [INFO] WebExplorer: [WebExplorer] Caelin is browsing the internet...
2025-07-14 00:42:34,740 [INFO] WebExplorer: [WebExplorer] Visiting https://www.reddit.com/r/todayilearned/hot.json
2025-07-14 00:42:34,741 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): www.reddit.com:443
2025-07-14 00:42:36,423 [DEBUG] urllib3.connectionpool: https://www.reddit.com:443 "GET /r/todayilearned/hot.json HTTP/1.1" 200 16928
2025-07-14 00:42:36,433 [DEBUG] urllib3.connectionpool: Starting new HTTPS connection (1): en.wikipedia.org:443
2025-07-14 00:42:36,716 [DEBUG] urllib3.connectionpool: https://en.wikipedia.org:443 "GET /wiki/History_of_prostitution_in_France HTTP/1.1" 200 55289
2025-07-14 00:42:37,012 [DEBUG] memory: Added memory ID 37: Web Discovery: TIL during WWI, syphilis treatment ...
2025-07-14 00:42:37,013 [INFO] WebExplorer: [Learning] Stored discovery in memory ID 37
2025-07-14 00:42:37,013 [INFO] WebExplorer: [WebExplorer] Found interesting: TIL during WWI, syphilis treatment was administere...
