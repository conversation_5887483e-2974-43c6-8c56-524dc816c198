import discord
import asyncio
import time
from Core import AIModel
from ProA import ProactiveEngine
from discord import app_commands
from discord.ext import commands, tasks # Make sure core.py is in the same folder

# Initialize your AI core
caelin = AIModel()

# Initialize proactive engine
proactive_engine = ProactiveEngine(caelin, idle_timeout=120)  # 2 minutes for Discord

intents = discord.Intents.default()
intents.messages = True
intents.message_content = True  # Required to read message content

# Use commands.Bot instead of discord.Client for slash commands
bot = commands.Bot(command_prefix='!', intents=intents)

# Dictionary to track active conversations per user
active_conversations = {}
CONVERSATION_TIMEOUT = 300  # 5 minutes in seconds

# Dictionary to track timeout tasks for cleanup
timeout_tasks = {}

# Dictionary to track proactive state per channel
proactive_channels = {}  # {channel_id: {'last_activity': timestamp, 'user_id': user_id, 'engine': ProactiveEngine}}
PROACTIVE_IDLE_TIME = 120  # 2 minutes before considering proactive message

@tasks.loop(seconds=30)  # Check every 30 seconds
async def proactive_message_loop():
    """Background task to send proactive messages"""
    current_time = time.time()

    for channel_id, data in list(proactive_channels.items()):
        try:
            # Check if enough time has passed since last activity
            time_since_activity = current_time - data['last_activity']

            if time_since_activity >= PROACTIVE_IDLE_TIME:
                channel = bot.get_channel(channel_id)
                if not channel:
                    # Channel no longer exists, clean up
                    del proactive_channels[channel_id]
                    continue

                # Update the proactive engine with idle context
                data['engine'].update_user_input("*silence*")

                # Check if should engage
                if data['engine'].should_engage():
                    try:
                        # Generate proactive message
                        proactive_msg = data['engine'].generate_proactive_message()

                        # Send the message
                        await channel.send(proactive_msg)

                        # Update last activity to prevent immediate re-triggering
                        data['last_activity'] = current_time

                        print(f"[Proactive] Sent message to channel {channel_id}")

                    except Exception as e:
                        print(f"[Proactive] Error sending message to {channel_id}: {e}")

        except Exception as e:
            print(f"[Proactive] Error processing channel {channel_id}: {e}")

@bot.event
async def on_ready():
    print(f"✅ Caelin is online as {bot.user}")
    await bot.change_presence(status=discord.Status.dnd, activity=discord.Game(name="Signalis.exe"))

    # Start the proactive message loop
    if not proactive_message_loop.is_running():
        proactive_message_loop.start()
        print("✅ Proactive message system started")

    try:
        synced = await bot.tree.sync()
        print(f"Synced {len(synced)} command(s)")
    except Exception as e:
        print(f"Failed to sync commands: {e}")

@bot.hybrid_command(name="ping", description="Pong!")
async def ping(ctx):
    latency = round(bot.latency * 1000)  # Convert to ms
    await ctx.reply(f"Pong! {latency}ms")

@bot.hybrid_command(name="proactive", description="Control proactive messaging")
async def proactive_control(ctx, action: str = "status"):
    """Control proactive messaging. Actions: start, stop, status"""
    channel_id = ctx.channel.id

    if action.lower() == "start":
        if channel_id not in proactive_channels:
            start_proactive_tracking(channel_id, ctx.author.id)
            await ctx.reply("✅ Started proactive messaging in this channel. I'll speak up if things get quiet!")
        else:
            await ctx.reply("ℹ️ Proactive messaging is already active in this channel.")

    elif action.lower() == "stop":
        if channel_id in proactive_channels:
            stop_proactive_tracking(channel_id)
            await ctx.reply("🔇 Stopped proactive messaging in this channel.")
        else:
            await ctx.reply("ℹ️ Proactive messaging is not active in this channel.")

    elif action.lower() == "status":
        if channel_id in proactive_channels:
            data = proactive_channels[channel_id]
            time_since = time.time() - data['last_activity']
            await ctx.reply(f"✅ Proactive messaging is **active**\n"
                          f"⏱️ Time since last activity: {time_since:.1f} seconds\n"
                          f"🎯 Will trigger after: {PROACTIVE_IDLE_TIME} seconds")
        else:
            await ctx.reply("❌ Proactive messaging is **not active** in this channel.\n"
                          f"Use `/proactive start` to enable it!")

    else:
        await ctx.reply("❓ Unknown action. Use: `start`, `stop`, or `status`")

def start_proactive_tracking(channel_id, user_id):
    """Start tracking a channel for proactive messages"""
    # Create a new proactive engine instance for this channel/user
    engine = ProactiveEngine(caelin, idle_timeout=PROACTIVE_IDLE_TIME)

    proactive_channels[channel_id] = {
        'last_activity': time.time(),
        'user_id': user_id,
        'engine': engine
    }
    print(f"[Proactive] Started tracking channel {channel_id} for user {user_id}")

def update_proactive_activity(channel_id, user_input=""):
    """Update the last activity time for a channel"""
    if channel_id in proactive_channels:
        proactive_channels[channel_id]['last_activity'] = time.time()
        if user_input:
            proactive_channels[channel_id]['engine'].update_user_input(user_input)

def stop_proactive_tracking(channel_id):
    """Stop tracking a channel for proactive messages"""
    if channel_id in proactive_channels:
        del proactive_channels[channel_id]
        print(f"[Proactive] Stopped tracking channel {channel_id}")

async def end_conversation_after_timeout(user_id):
    """End conversation after timeout period"""
    try:
        await asyncio.sleep(CONVERSATION_TIMEOUT)
        if user_id in active_conversations:
            del active_conversations[user_id]
        if user_id in timeout_tasks:
            del timeout_tasks[user_id]
    except asyncio.CancelledError:
        # Task was cancelled, clean up
        if user_id in timeout_tasks:
            del timeout_tasks[user_id]

async def is_message_for_caelin(message):
    """Check if message is likely directed at Caelin"""
    content = message.content.lower()
    
    # Always respond if directly mentioned
    if bot.user.mentioned_in(message):
        return True
    
    # Skip if message is clearly for someone else
    # Check for @mentions of other users
    if message.mentions and bot.user not in message.mentions:
        return False
    
    # Check for common patterns that indicate talking to someone else
    talking_to_others = [
        'hey @', 'hi @', 'hello @',  # Greeting others
        'thanks @', 'thank you @',   # Thanking others
        'good morning @', 'good night @',  # Time-based greetings
    ]
    
    for pattern in talking_to_others:
        if pattern in content:
            return False
    
    # Check if replying to someone else's message
    if message.reference:
        try:
            replied_message = await message.channel.fetch_message(message.reference.message_id)
            # If they're replying to someone else (not Caelin), probably not for Caelin
            if replied_message.author != bot.user:
                return False
        except:
            pass  # If we can't fetch the message, continue with other checks
    
    # Check for conversational patterns that suggest talking to others
    indirect_patterns = [
        'lol', 'lmao', 'haha', 'omg',  # Casual reactions
        'same', 'mood', 'fr', 'facts',  # Agreement with others
        'brb', 'gtg', 'ttyl',  # Leaving messages
        'gm', 'gn', 'good morning everyone', 'good night everyone',  # General greetings
    ]
    
    # If message is ONLY these patterns, probably not for Caelin
    words = content.split()
    if len(words) <= 2 and any(pattern in content for pattern in indirect_patterns):
        return False
    
    # If we get here, assume it's for Caelin
    return True

def start_conversation_timeout(user_id):
    """Start or restart the conversation timeout for a user"""
    # Cancel existing timeout task if it exists
    if user_id in timeout_tasks:
        timeout_tasks[user_id].cancel()
    
    # Create new timeout task
    timeout_tasks[user_id] = asyncio.create_task(end_conversation_after_timeout(user_id))

@bot.event
async def on_message(message):
    if message.author.bot:
        return
    
    user_id = message.author.id
    content = message.content.lower()
    
    # Check if user is starting a new conversation by mentioning Caelin
    if "caelin" in content or bot.user.mentioned_in(message):
        # Start/restart conversation tracking for this user
        active_conversations[user_id] = True
        start_conversation_timeout(user_id)

        # Start proactive tracking for this channel
        start_proactive_tracking(message.channel.id, user_id)

        # Remove "caelin" from the prompt for cleaner processing
        username = message.author.display_name
        prompt = f"User ({username}): {content.replace('caelin', '').strip()}"

        if not prompt:
            await message.channel.send("Yes? 😊")
            return

        await message.channel.typing()
        try:
            response = await caelin.agenerate(prompt, user_id=str(user_id))
            await message.reply(response, mention_author=False)

            # Update proactive activity
            update_proactive_activity(message.channel.id, prompt)

        except Exception as e:
            print(f"Error generating response: {e}")
            await message.reply("Sorry, I encountered an error processing your message.", mention_author=False)
        return
    
    # Check if user has an active conversation AND message seems to be for Caelin
    if user_id in active_conversations and await is_message_for_caelin(message):
        # Reset the timeout timer
        start_conversation_timeout(user_id)

        prompt = message.content
        await message.channel.typing()
        try:
            response = await caelin.agenerate(prompt, user_id=str(user_id))
            await message.reply(response, mention_author=False)

            # Update proactive activity
            update_proactive_activity(message.channel.id, prompt)

        except Exception as e:
            print(f"Error generating response: {e}")
            await message.reply("Sorry, I encountered an error processing your message.", mention_author=False)
        return
    
    # Also respond to all DMs regardless of conversation state
    if isinstance(message.channel, discord.DMChannel):
        # Start proactive tracking for DMs too
        start_proactive_tracking(message.channel.id, user_id)

        prompt = message.content
        await message.channel.typing()
        try:
            response = await caelin.agenerate(prompt, user_id=str(user_id))
            await message.reply(response, mention_author=False)

            # Update proactive activity
            update_proactive_activity(message.channel.id, prompt)

        except Exception as e:
            print(f"Error generating response: {e}")
            await message.reply("Sorry, I encountered an error processing your message.", mention_author=False)
    
    # Process commands
    await bot.process_commands(message)

# Replace YOUR_DISCORD_BOT_TOKEN_HERE with your actual Discord bot token
DISCORD_TOKEN = "MTI5ODc3NDg3OTA4ODE0ODU0MA.GY_5P8.MXO755RXeaXnNmEkPXNsK4lRDaRkxCIa2hjSD8"
bot.run(DISCORD_TOKEN)