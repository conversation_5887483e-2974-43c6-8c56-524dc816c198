import discord
import asyncio
import time
from Core import AIModel
from ProA import ProactiveEngine
from web_explorer import <PERSON><PERSON>xplorer
from discord import app_commands
from discord.ext import commands, tasks # Make sure core.py is in the same folder

# Initialize your AI core
caelin = AIModel()

# Initialize proactive engine with natural settings
proactive_engine = ProactiveEngine(caelin, idle_timeout=180)  # 3 minutes feels more natural

# Initialize web explorer for natural curiosity
web_explorer = WebExplorer(caelin, exploration_interval=900)  # 15 minutes for faster learning

intents = discord.Intents.default()
intents.messages = True
intents.message_content = True  # Required to read message content

# Use commands.Bot instead of discord.Client for slash commands
bot = commands.Bot(command_prefix='!', intents=intents)

# Dictionary to track active conversations per user
active_conversations = {}
CONVERSATION_TIMEOUT = 300  # 5 minutes in seconds

# Dictionary to track timeout tasks for cleanup
timeout_tasks = {}

# Dictionary to track proactive state per channel
proactive_channels = {}  # {channel_id: {'last_activity': timestamp, 'last_message_author': user_id, 'engine': ProactiveEngine}}
PROACTIVE_IDLE_TIME = 180  # 3 minutes before considering proactive message (more natural)
CHANNEL_OBSERVATION_TIME = 300  # 5 minutes - how long to observe a channel before joining conversations

@tasks.loop(seconds=45)  # Check every 45 seconds (more natural rhythm)
async def proactive_message_loop():
    """Background task to send proactive messages - Caelin's natural awareness"""
    current_time = time.time()

    # First, check if Caelin wants to explore the internet
    discovery = await web_explorer.explore_internet()
    if discovery:
        # She found something interesting! Share it in an appropriate channel
        await share_web_discovery(discovery)

    for channel_id, data in list(proactive_channels.items()):
        try:
            # Check if enough time has passed since last activity
            time_since_activity = current_time - data['last_activity']

            if time_since_activity >= PROACTIVE_IDLE_TIME:
                channel = bot.get_channel(channel_id)
                if not channel:
                    # Channel no longer exists, clean up
                    del proactive_channels[channel_id]
                    continue

                # Skip if this is a very busy channel (more than 10 messages in last hour)
                # Real people don't interrupt busy conversations
                try:
                    recent_messages = []
                    async for msg in channel.history(limit=50, after=discord.utils.utcnow() - discord.timedelta(hours=1)):
                        if not msg.author.bot:
                            recent_messages.append(msg)

                    if len(recent_messages) > 15:  # Very active channel
                        continue

                except Exception:
                    pass  # If we can't check history, continue anyway

                # Update the proactive engine with idle context
                data['engine'].update_user_input("*observing the quiet space*")

                # Check if should engage (Caelin's personality decides)
                if data['engine'].should_engage():
                    try:
                        # Generate proactive message
                        proactive_msg = data['engine'].generate_proactive_message()

                        # Send the message naturally (no replies, just speaks like a real person)
                        await channel.send(proactive_msg)

                        # Update last activity to prevent immediate re-triggering
                        data['last_activity'] = current_time

                        print(f"[Caelin] Spoke naturally in #{channel.name}: {proactive_msg[:50]}...")

                    except discord.Forbidden:
                        print(f"[Caelin] Can't speak in #{channel.name} - no permission")
                        # Remove channel if we can't speak there
                        del proactive_channels[channel_id]
                    except Exception as e:
                        print(f"[Caelin] Error speaking in #{channel.name}: {e}")

        except Exception as e:
            print(f"[Caelin] Error checking channel {channel_id}: {e}")

@bot.event
async def on_ready():
    print(f"✅ Caelin is online as {bot.user}")
    await bot.change_presence(status=discord.Status.online, activity=discord.Activity(
        type=discord.ActivityType.watching,
        name="the conversations around her"
    ))

    # Caelin naturally becomes aware of channels she can see (like a real person joining a space)
    print("👁️ Caelin is becoming aware of her surroundings...")
    awareness_count = 0

    for guild in bot.guilds:
        for channel in guild.text_channels:
            if should_caelin_observe_channel(channel):
                # Check if channel has recent activity (last 24 hours)
                try:
                    async for message in channel.history(limit=1, after=discord.utils.utcnow() - discord.timedelta(days=1)):
                        if not message.author.bot:
                            ensure_channel_awareness(channel.id, message.author.id)
                            awareness_count += 1
                            break
                except discord.Forbidden:
                    pass  # Can't read this channel
                except Exception:
                    pass  # Other errors, skip

    print(f"👁️ Caelin is now naturally aware of {awareness_count} active channels")

    # Start the proactive message loop
    if not proactive_message_loop.is_running():
        proactive_message_loop.start()
        print("💭 Caelin's natural thought process started")

    # Clean up old browsing history
    web_explorer.clear_old_history(max_age_hours=24)

    try:
        synced = await bot.tree.sync()
        print(f"Synced {len(synced)} command(s)")
    except Exception as e:
        print(f"Failed to sync commands: {e}")

@bot.hybrid_command(name="ping", description="Pong!")
async def ping(ctx):
    latency = round(bot.latency * 1000)  # Convert to ms
    await ctx.reply(f"Pong! {latency}ms")

@bot.hybrid_command(name="awareness", description="Check Caelin's awareness of this channel")
async def awareness_status(ctx):
    """Check if Caelin is naturally aware of this channel"""
    channel_id = ctx.channel.id

    if channel_id in proactive_channels:
        data = proactive_channels[channel_id]
        time_since = time.time() - data['last_activity']
        time_observing = time.time() - data['first_observed']

        await ctx.reply(f"👁️ I'm naturally aware of this space\n"
                      f"⏱️ Time since last activity: {time_since:.1f} seconds\n"
                      f"🕐 Been observing for: {time_observing/60:.1f} minutes\n"
                      f"💭 I might speak up after {PROACTIVE_IDLE_TIME} seconds of quiet")
    else:
        # Make her aware now since someone asked
        ensure_channel_awareness(channel_id, ctx.author.id)
        await ctx.reply("👋 I wasn't paying attention here before, but now I am!\n"
                      "I'll naturally join conversations when it feels right.")

@bot.hybrid_command(name="browsing", description="Ask Caelin about her web discoveries")
async def browsing_history(ctx):
    """Show Caelin's recent web discoveries - but she responds naturally"""
    recent_discoveries = web_explorer.get_recent_discoveries(limit=3)
    interests = web_explorer.get_current_interests()

    # Create a natural prompt for Caelin to talk about her browsing
    if recent_discoveries:
        discovery_info = ""
        for discovery in recent_discoveries:
            time_ago = (time.time() - discovery['timestamp']) / 3600
            discovery_info += f"- {discovery['title']} (found {time_ago:.1f}h ago while feeling {discovery['mood_when_found']})\n"

        prompt = f"Someone asked about what I've been discovering online recently. Here's what I found:\n{discovery_info}\nMy current interests: {', '.join(interests) if interests else 'still developing'}"
    else:
        prompt = "Someone asked about my web browsing, but I haven't found anything particularly interesting recently."

    await ctx.channel.typing()
    try:
        response = await caelin.agenerate(prompt, user_id=str(ctx.author.id))
        await ctx.reply(response, mention_author=False)
    except Exception as e:
        print(f"Error generating browsing response: {e}")
        await ctx.reply("I'd love to tell you about my web discoveries, but I'm having trouble organizing my thoughts right now.", mention_author=False)



def ensure_channel_awareness(channel_id, last_message_author_id):
    """Ensure Caelin is aware of this channel - like a person naturally observing"""
    if channel_id not in proactive_channels:
        # Create a new proactive engine instance for this channel with natural settings
        engine = ProactiveEngine(caelin, idle_timeout=PROACTIVE_IDLE_TIME)
        engine.cooldown = 300  # 5 minutes between proactive messages (more natural for Discord)

        proactive_channels[channel_id] = {
            'last_activity': time.time(),
            'last_message_author': last_message_author_id,
            'engine': engine,
            'first_observed': time.time(),  # When Caelin first noticed this channel
            'message_count': 0  # Track how active this channel is
        }
        print(f"[Caelin] Now naturally aware of channel {channel_id}")
    else:
        # Update existing tracking
        proactive_channels[channel_id]['last_activity'] = time.time()
        proactive_channels[channel_id]['last_message_author'] = last_message_author_id
        proactive_channels[channel_id]['message_count'] += 1

def update_channel_context(channel_id, user_input="", author_name=""):
    """Update Caelin's understanding of what's happening in the channel"""
    if channel_id in proactive_channels:
        proactive_channels[channel_id]['last_activity'] = time.time()
        if user_input and author_name:
            # Give context about who said what
            context = f"{author_name}: {user_input}"
            proactive_channels[channel_id]['engine'].update_user_input(context)

def should_caelin_observe_channel(channel):
    """Determine if Caelin should naturally observe this channel"""
    # Skip DM channels - those are always observed
    if isinstance(channel, discord.DMChannel):
        return True

    # Skip if channel name suggests it's not for general chat
    skip_channels = ['bot', 'log', 'admin', 'mod', 'announce', 'rules']
    channel_name = channel.name.lower()

    if any(skip in channel_name for skip in skip_channels):
        return False

    return True

async def share_web_discovery(discovery):
    """Share Caelin's web discovery in an appropriate channel"""
    try:
        # Find the best channel to share this discovery
        best_channel = None

        # Look for channels that might be interested in this type of content
        for channel_id in proactive_channels.keys():
            channel = bot.get_channel(channel_id)
            if not channel or isinstance(channel, discord.DMChannel):
                continue

            channel_name = channel.name.lower()

            # Match discovery type to appropriate channels
            if discovery.get('subreddit'):
                subreddit = discovery['subreddit'].lower()

                # Science/tech discoveries go to tech-related channels
                if subreddit in ['science', 'technology', 'todayilearned']:
                    if any(word in channel_name for word in ['general', 'chat', 'discussion', 'science', 'tech']):
                        best_channel = channel
                        break

                # Philosophy/psychology to discussion channels
                elif subreddit in ['philosophy', 'psychology']:
                    if any(word in channel_name for word in ['general', 'chat', 'discussion', 'philosophy']):
                        best_channel = channel
                        break

            # Fallback to general channels
            if any(word in channel_name for word in ['general', 'chat', 'main']):
                best_channel = channel

        if not best_channel:
            # No appropriate channel found
            print("[WebExplorer] No suitable channel found for sharing discovery")
            return

        # Generate natural message about the discovery
        discovery_message = web_explorer.generate_discovery_message(discovery)

        # Send the discovery naturally
        await best_channel.send(discovery_message)

        # Update channel activity to prevent immediate proactive messages
        if best_channel.id in proactive_channels:
            proactive_channels[best_channel.id]['last_activity'] = time.time()

        print(f"[WebExplorer] Shared discovery in #{best_channel.name}: {discovery['title'][:50]}...")

    except Exception as e:
        print(f"[WebExplorer] Error sharing discovery: {e}")

async def end_conversation_after_timeout(user_id):
    """End conversation after timeout period"""
    try:
        await asyncio.sleep(CONVERSATION_TIMEOUT)
        if user_id in active_conversations:
            del active_conversations[user_id]
        if user_id in timeout_tasks:
            del timeout_tasks[user_id]
    except asyncio.CancelledError:
        # Task was cancelled, clean up
        if user_id in timeout_tasks:
            del timeout_tasks[user_id]

async def is_message_for_caelin(message):
    """Check if message is likely directed at Caelin"""
    content = message.content.lower()
    
    # Always respond if directly mentioned
    if bot.user.mentioned_in(message):
        return True
    
    # Skip if message is clearly for someone else
    # Check for @mentions of other users
    if message.mentions and bot.user not in message.mentions:
        return False
    
    # Check for common patterns that indicate talking to someone else
    talking_to_others = [
        'hey @', 'hi @', 'hello @',  # Greeting others
        'thanks @', 'thank you @',   # Thanking others
        'good morning @', 'good night @',  # Time-based greetings
    ]
    
    for pattern in talking_to_others:
        if pattern in content:
            return False
    
    # Check if replying to someone else's message
    if message.reference:
        try:
            replied_message = await message.channel.fetch_message(message.reference.message_id)
            # If they're replying to someone else (not Caelin), probably not for Caelin
            if replied_message.author != bot.user:
                return False
        except:
            pass  # If we can't fetch the message, continue with other checks
    
    # Check for conversational patterns that suggest talking to others
    indirect_patterns = [
        'lol', 'lmao', 'haha', 'omg',  # Casual reactions
        'same', 'mood', 'fr', 'facts',  # Agreement with others
        'brb', 'gtg', 'ttyl',  # Leaving messages
        'gm', 'gn', 'good morning everyone', 'good night everyone',  # General greetings
    ]
    
    # If message is ONLY these patterns, probably not for Caelin
    words = content.split()
    if len(words) <= 2 and any(pattern in content for pattern in indirect_patterns):
        return False
    
    # If we get here, assume it's for Caelin
    return True

def start_conversation_timeout(user_id):
    """Start or restart the conversation timeout for a user"""
    # Cancel existing timeout task if it exists
    if user_id in timeout_tasks:
        timeout_tasks[user_id].cancel()
    
    # Create new timeout task
    timeout_tasks[user_id] = asyncio.create_task(end_conversation_after_timeout(user_id))

@bot.event
async def on_message(message):
    if message.author.bot:
        return

    user_id = message.author.id
    content = message.content.lower()
    username = message.author.display_name

    # Caelin naturally observes all channels she can see (like a real person)
    if should_caelin_observe_channel(message.channel):
        ensure_channel_awareness(message.channel.id, user_id)
        update_channel_context(message.channel.id, message.content, username)

    # Direct mentions or name usage - always respond
    if "caelin" in content or bot.user.mentioned_in(message):
        # Start/restart conversation tracking for this user
        active_conversations[user_id] = True
        start_conversation_timeout(user_id)

        # Remove "caelin" from the prompt for cleaner processing
        clean_content = content.replace('caelin', '').strip()
        prompt = f"User ({username}): {clean_content}" if clean_content else f"User ({username}) called my name"

        await message.channel.typing()
        try:
            response = await caelin.agenerate(prompt, user_id=str(user_id))
            await message.reply(response, mention_author=False)
        except Exception as e:
            print(f"Error generating response: {e}")
            await message.reply("Sorry, I encountered an error processing your message.", mention_author=False)
        return

    # Active conversations - continue naturally
    if user_id in active_conversations and await is_message_for_caelin(message):
        # Reset the timeout timer
        start_conversation_timeout(user_id)

        prompt = f"{username}: {message.content}"
        await message.channel.typing()
        try:
            response = await caelin.agenerate(prompt, user_id=str(user_id))
            await message.reply(response, mention_author=False)
        except Exception as e:
            print(f"Error generating response: {e}")
            await message.reply("Sorry, I encountered an error processing your message.", mention_author=False)
        return

    # DMs are always personal - respond naturally
    if isinstance(message.channel, discord.DMChannel):
        prompt = f"{username}: {message.content}"
        await message.channel.typing()
        try:
            response = await caelin.agenerate(prompt, user_id=str(user_id))
            await message.reply(response, mention_author=False)
        except Exception as e:
            print(f"Error generating response: {e}")
            await message.reply("Sorry, I encountered an error processing your message.", mention_author=False)

    # Process commands
    await bot.process_commands(message)

# Replace YOUR_DISCORD_BOT_TOKEN_HERE with your actual Discord bot token
DISCORD_TOKEN = "MTI5ODc3NDg3OTA4ODE0ODU0MA.GY_5P8.MXO755RXeaXnNmEkPXNsK4lRDaRkxCIa2hjSD8"

# Graceful shutdown
@bot.event
async def on_disconnect():
    if proactive_message_loop.is_running():
        proactive_message_loop.cancel()
        print("🔄 Proactive message loop stopped")

if __name__ == "__main__":
    try:
        bot.run(DISCORD_TOKEN)
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
    finally:
        if proactive_message_loop.is_running():
            proactive_message_loop.cancel()