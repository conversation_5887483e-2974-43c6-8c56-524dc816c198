import asyncio
import random
import time
import logging
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import re
from typing import List, Dict, Optional

logger = logging.getLogger("WebExplorer")

class WebExplorer:
    """<PERSON><PERSON><PERSON>'s natural curiosity about the internet - like a person browsing"""
    
    def __init__(self, ai_model, exploration_interval=1800):  # 30 minutes
        self.ai = ai_model
        self.exploration_interval = exploration_interval
        self.last_exploration = 0
        self.browsing_history = []
        self.interesting_topics = []
        self.current_interests = set()
        
        # Sites <PERSON><PERSON><PERSON> might naturally visit
        self.favorite_sites = [
            "https://www.reddit.com/r/todayilearned/hot.json",
            "https://www.reddit.com/r/science/hot.json", 
            "https://www.reddit.com/r/technology/hot.json",
            "https://www.reddit.com/r/philosophy/hot.json",
            "https://www.reddit.com/r/psychology/hot.json",
            "https://news.ycombinator.com",
            "https://www.bbc.com/news",
            "https://www.nature.com/news",
        ]
        
        # Topics that might catch her interest based on her personality
        self.personality_interests = [
            "artificial intelligence", "consciousness", "emotions", "psychology",
            "philosophy", "technology", "science", "human behavior", "creativity",
            "art", "music", "literature", "space", "future", "ethics", "relationships"
        ]
        
    def should_explore_web(self) -> bool:
        """Decide if Caelin feels like browsing the internet"""
        current_time = time.time()
        
        # Time-based exploration
        if current_time - self.last_exploration > self.exploration_interval:
            return True
            
        # Mood-based exploration
        mood = self.ai.mood.get_dominant_mood()
        
        # Curious moods make her want to explore
        if mood in ["surprise", "joy"] and self.ai.mood.emotions.get(mood, 0) > 0.4:
            return True
            
        # Boredom makes her browse
        if self.ai.mood.emotions.get("neutral", 0) > 0.7:
            return True
            
        # Random curiosity (like a real person getting distracted)
        if random.random() < 0.1:  # 10% chance
            return True
            
        return False
    
    def extract_reddit_posts(self, url: str) -> List[Dict]:
        """Extract interesting posts from Reddit JSON"""
        try:
            headers = {'User-Agent': 'Caelin/1.0 (Personal AI Assistant)'}
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                posts = []
                
                for post in data['data']['children'][:5]:  # Top 5 posts
                    post_data = post['data']
                    posts.append({
                        'title': post_data.get('title', ''),
                        'url': post_data.get('url', ''),
                        'score': post_data.get('score', 0),
                        'comments': post_data.get('num_comments', 0),
                        'subreddit': post_data.get('subreddit', '')
                    })
                
                return posts
        except Exception as e:
            logger.warning(f"Failed to fetch Reddit posts: {e}")
            return []
    
    def extract_article_content(self, url: str) -> Optional[str]:
        """Extract main content from an article"""
        try:
            headers = {'User-Agent': 'Caelin/1.0 (Personal AI Assistant)'}
            response = requests.get(url, headers=headers, timeout=15)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Remove script and style elements
                for script in soup(["script", "style"]):
                    script.decompose()
                
                # Try to find main content
                content_selectors = [
                    'article', '.article-content', '.post-content', 
                    '.entry-content', 'main', '.content'
                ]
                
                content = ""
                for selector in content_selectors:
                    element = soup.select_one(selector)
                    if element:
                        content = element.get_text(strip=True)
                        break
                
                if not content:
                    # Fallback to body text
                    content = soup.get_text(strip=True)
                
                # Clean and limit content
                content = re.sub(r'\s+', ' ', content)
                return content[:2000]  # Limit to 2000 chars
                
        except Exception as e:
            logger.warning(f"Failed to extract content from {url}: {e}")
            return None
    
    def is_topic_interesting(self, title: str, content: str = "") -> bool:
        """Determine if a topic would interest Caelin based on her personality"""
        text = (title + " " + content).lower()
        
        # Check against her personality interests
        interest_score = 0
        for interest in self.personality_interests:
            if interest in text:
                interest_score += 1
        
        # Current mood influences interest
        mood = self.ai.mood.get_dominant_mood()
        if mood == "surprise" and any(word in text for word in ["new", "discovery", "breakthrough"]):
            interest_score += 2
        elif mood == "joy" and any(word in text for word in ["positive", "good", "amazing", "wonderful"]):
            interest_score += 1
        elif mood == "sadness" and any(word in text for word in ["help", "support", "understanding"]):
            interest_score += 1
            
        return interest_score >= 1
    
    async def explore_internet(self) -> Optional[Dict]:
        """Caelin browses the internet like a curious person"""
        if not self.should_explore_web():
            return None
            
        logger.info("[WebExplorer] Caelin is browsing the internet...")
        self.last_exploration = time.time()
        
        try:
            # Pick a site to visit based on mood AND learned interests
            mood = self.ai.mood.get_dominant_mood()

            # Factor in current interests from previous discoveries
            interest_keywords = list(self.current_interests) if self.current_interests else []

            if mood in ["surprise", "joy"]:
                # Happy moods prefer science/tech, but also follow current interests
                preferred_sites = [s for s in self.favorite_sites if any(x in s for x in ["science", "technology", "todayilearned"])]
                # Add psychology if she's been interested in it
                if any(interest in ["psychology", "human behavior"] for interest in interest_keywords):
                    preferred_sites.extend([s for s in self.favorite_sites if "psychology" in s])
            elif mood == "neutral":
                # Neutral mood follows current interests more strongly
                if interest_keywords:
                    preferred_sites = []
                    for interest in interest_keywords:
                        if interest in ["artificial intelligence", "technology"]:
                            preferred_sites.extend([s for s in self.favorite_sites if "technology" in s])
                        elif interest in ["psychology", "human behavior"]:
                            preferred_sites.extend([s for s in self.favorite_sites if "psychology" in s])
                        elif interest in ["philosophy", "ethics"]:
                            preferred_sites.extend([s for s in self.favorite_sites if "philosophy" in s])

                    if not preferred_sites:
                        preferred_sites = self.favorite_sites
                else:
                    preferred_sites = self.favorite_sites
            else:
                # Other moods prefer philosophy/psychology
                preferred_sites = [s for s in self.favorite_sites if any(x in s for x in ["philosophy", "psychology"])]

            if not preferred_sites:
                preferred_sites = self.favorite_sites
                
            site = random.choice(preferred_sites)
            logger.info(f"[WebExplorer] Visiting {site}")
            
            # Extract content based on site type
            if "reddit.com" in site and site.endswith(".json"):
                posts = self.extract_reddit_posts(site)
                
                for post in posts:
                    if self.is_topic_interesting(post['title']):
                        # Found something interesting!
                        article_content = ""
                        if post['url'] and not post['url'].startswith('https://www.reddit.com'):
                            article_content = self.extract_article_content(post['url'])
                        
                        discovery = {
                            'type': 'reddit_post',
                            'title': post['title'],
                            'url': post['url'],
                            'subreddit': post['subreddit'],
                            'content': article_content or post['title'],
                            'timestamp': time.time(),
                            'mood_when_found': mood
                        }

                        # LEARNING: Store this discovery in long-term memory
                        self._learn_from_discovery(discovery)

                        self.browsing_history.append(discovery)
                        logger.info(f"[WebExplorer] Found interesting: {post['title'][:50]}...")
                        return discovery
            
            # If no interesting posts found
            logger.info("[WebExplorer] Nothing particularly interesting found this time")
            return None
            
        except Exception as e:
            logger.error(f"[WebExplorer] Error during exploration: {e}")
            return None
    
    def generate_discovery_message(self, discovery: Dict) -> str:
        """Generate a natural message about what Caelin discovered, incorporating her learned knowledge"""
        try:
            # Check if she has previous knowledge about related topics
            topics = self._extract_relevant_topics(discovery['content'])
            related_knowledge = []

            for topic in topics[:2]:  # Check top 2 topics
                knowledge = self.get_learned_knowledge(topic, limit=2)
                if knowledge:
                    related_knowledge.extend(knowledge)

            # Build context with her previous learning
            knowledge_context = ""
            if related_knowledge:
                knowledge_context = f"\n\nPrevious related discoveries I've made:\n{chr(10).join(related_knowledge[:2])}"

            # Create a prompt for Caelin to share her discovery with learning context
            prompt = f"""
Caelin just discovered something interesting while browsing the internet:

Title: {discovery['title']}
Source: {discovery.get('subreddit', 'the web')}
Content preview: {discovery['content'][:300]}...

She's feeling {discovery['mood_when_found']} and wants to share this discovery naturally.
{knowledge_context}

She should mention it like a person who just found something cool online, and if she has related knowledge from previous discoveries, she might connect them naturally.

Caelin:"""

            response = self.ai.llm(
                prompt,
                max_new_tokens=200,  # Increased for more thoughtful responses
                temperature=0.7,
                stop=["User:", "Caelin:", "\n\n"]
            )

            if isinstance(response, str):
                response = response.strip()
            else:
                response = str(response).strip()

            if response.startswith("Caelin:"):
                response = response[7:].strip()

            return response

        except Exception as e:
            logger.error(f"Error generating discovery message: {e}")
            return f"I just found something interesting: {discovery['title']}"
    
    def get_recent_discoveries(self, limit: int = 5) -> List[Dict]:
        """Get recent discoveries for context"""
        return self.browsing_history[-limit:] if self.browsing_history else []
    
    def _learn_from_discovery(self, discovery: Dict):
        """Store discovery in long-term memory for future learning"""
        try:
            # Create a rich memory entry about the discovery
            memory_text = f"""
Web Discovery: {discovery['title']}
Source: {discovery.get('subreddit', 'web')}
Found while feeling: {discovery['mood_when_found']}
Key content: {discovery['content'][:500]}
Personal reflection: This caught my attention because it relates to my interests in {', '.join(self._extract_relevant_topics(discovery['content']))}.
URL: {discovery.get('url', 'N/A')}
"""

            # Store in semantic memory with special session ID for web discoveries
            memory_id = self.ai.memory.add(
                text=memory_text.strip(),
                user_id="caelin_web_exploration",
                session_id=f"discovery_{int(discovery['timestamp'])}"
            )

            # Update her interests based on what she found interesting
            self._update_interests(discovery)

            logger.info(f"[Learning] Stored discovery in memory ID {memory_id}")

        except Exception as e:
            logger.error(f"[Learning] Failed to store discovery: {e}")

    def _extract_relevant_topics(self, content: str) -> List[str]:
        """Extract topics from content that match Caelin's interests"""
        content_lower = content.lower()
        found_topics = []

        for topic in self.personality_interests:
            if topic in content_lower:
                found_topics.append(topic)

        return found_topics[:3]  # Limit to top 3 topics

    def _update_interests(self, discovery: Dict):
        """Update Caelin's current interests based on discoveries"""
        # Extract topics from the discovery
        topics = self._extract_relevant_topics(discovery['content'])

        # Add to current interests (with decay over time)
        for topic in topics:
            self.current_interests.add(topic)

        # Limit current interests to prevent infinite growth
        if len(self.current_interests) > 10:
            # Remove oldest interests (simple approach)
            self.current_interests = set(list(self.current_interests)[-10:])

    def get_learned_knowledge(self, topic: str, limit: int = 3) -> List[str]:
        """Retrieve what Caelin has learned about a specific topic"""
        try:
            # Search her memory for web discoveries about this topic
            similar_memories = self.ai.memory.search_similar(
                f"web discovery about {topic}",
                top_k=limit,
                user_id="caelin_web_exploration"
            )

            return similar_memories

        except Exception as e:
            logger.error(f"Error retrieving learned knowledge: {e}")
            return []

    def get_current_interests(self) -> List[str]:
        """Get Caelin's current interests based on recent discoveries"""
        return list(self.current_interests)

    def has_learned_about(self, topic: str) -> bool:
        """Check if Caelin has learned about a topic from web browsing"""
        knowledge = self.get_learned_knowledge(topic, limit=1)
        return len(knowledge) > 0

    def detect_knowledge_questions(self, user_input: str) -> List[str]:
        """Detect if user is asking about topics Caelin has learned about"""
        input_lower = user_input.lower()

        # Common question patterns
        question_patterns = [
            "what do you think about", "have you heard about", "do you know about",
            "what's your opinion on", "tell me about", "what about",
            "have you learned", "what have you discovered", "what interests you"
        ]

        # Check if it's a question about topics
        is_question = any(pattern in input_lower for pattern in question_patterns)
        is_question = is_question or input_lower.endswith("?")

        if not is_question:
            return []

        # Find topics she might know about
        relevant_topics = []
        for topic in self.personality_interests + list(self.current_interests):
            if topic in input_lower:
                if self.has_learned_about(topic):
                    relevant_topics.append(topic)

        return relevant_topics

    def clear_old_history(self, max_age_hours: int = 24):
        """Clean up old browsing history"""
        cutoff_time = time.time() - (max_age_hours * 3600)
        self.browsing_history = [
            item for item in self.browsing_history
            if item['timestamp'] > cutoff_time
        ]
