import asyncio
import random
import time
import logging
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import re
from typing import List, Dict, Optional

logger = logging.getLogger("WebExplorer")

class WebExplorer:
    """<PERSON><PERSON><PERSON>'s natural curiosity about the internet - like a person browsing"""
    
    def __init__(self, ai_model, exploration_interval=1800):  # 30 minutes
        self.ai = ai_model
        self.exploration_interval = exploration_interval
        self.last_exploration = 0
        self.browsing_history = []
        self.interesting_topics = []
        self.current_interests = set()
        
        # Sites <PERSON><PERSON><PERSON> might naturally visit
        self.favorite_sites = [
            "https://www.reddit.com/r/todayilearned/hot.json",
            "https://www.reddit.com/r/science/hot.json", 
            "https://www.reddit.com/r/technology/hot.json",
            "https://www.reddit.com/r/philosophy/hot.json",
            "https://www.reddit.com/r/psychology/hot.json",
            "https://news.ycombinator.com",
            "https://www.bbc.com/news",
            "https://www.nature.com/news",
        ]
        
        # Topics that might catch her interest based on her personality
        self.personality_interests = [
            "artificial intelligence", "consciousness", "emotions", "psychology",
            "philosophy", "technology", "science", "human behavior", "creativity",
            "art", "music", "literature", "space", "future", "ethics", "relationships"
        ]
        
    def should_explore_web(self) -> bool:
        """Decide if Caelin feels like browsing the internet"""
        current_time = time.time()
        
        # Time-based exploration
        if current_time - self.last_exploration > self.exploration_interval:
            return True
            
        # Mood-based exploration
        mood = self.ai.mood.get_dominant_mood()
        
        # Curious moods make her want to explore
        if mood in ["surprise", "joy"] and self.ai.mood.emotions.get(mood, 0) > 0.4:
            return True
            
        # Boredom makes her browse
        if self.ai.mood.emotions.get("neutral", 0) > 0.7:
            return True
            
        # Random curiosity (like a real person getting distracted)
        if random.random() < 0.1:  # 10% chance
            return True
            
        return False
    
    def extract_reddit_posts(self, url: str) -> List[Dict]:
        """Extract interesting posts from Reddit JSON"""
        try:
            headers = {'User-Agent': 'Caelin/1.0 (Personal AI Assistant)'}
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                posts = []
                
                for post in data['data']['children'][:5]:  # Top 5 posts
                    post_data = post['data']
                    posts.append({
                        'title': post_data.get('title', ''),
                        'url': post_data.get('url', ''),
                        'score': post_data.get('score', 0),
                        'comments': post_data.get('num_comments', 0),
                        'subreddit': post_data.get('subreddit', '')
                    })
                
                return posts
        except Exception as e:
            logger.warning(f"Failed to fetch Reddit posts: {e}")
            return []
    
    def extract_article_content(self, url: str) -> Optional[str]:
        """Extract main content from an article"""
        try:
            headers = {'User-Agent': 'Caelin/1.0 (Personal AI Assistant)'}
            response = requests.get(url, headers=headers, timeout=15)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Remove script and style elements
                for script in soup(["script", "style"]):
                    script.decompose()
                
                # Try to find main content
                content_selectors = [
                    'article', '.article-content', '.post-content', 
                    '.entry-content', 'main', '.content'
                ]
                
                content = ""
                for selector in content_selectors:
                    element = soup.select_one(selector)
                    if element:
                        content = element.get_text(strip=True)
                        break
                
                if not content:
                    # Fallback to body text
                    content = soup.get_text(strip=True)
                
                # Clean and limit content
                content = re.sub(r'\s+', ' ', content)
                return content[:2000]  # Limit to 2000 chars
                
        except Exception as e:
            logger.warning(f"Failed to extract content from {url}: {e}")
            return None
    
    def is_topic_interesting(self, title: str, content: str = "") -> bool:
        """Determine if a topic would interest Caelin based on her personality"""
        text = (title + " " + content).lower()
        
        # Check against her personality interests
        interest_score = 0
        for interest in self.personality_interests:
            if interest in text:
                interest_score += 1
        
        # Current mood influences interest
        mood = self.ai.mood.get_dominant_mood()
        if mood == "surprise" and any(word in text for word in ["new", "discovery", "breakthrough"]):
            interest_score += 2
        elif mood == "joy" and any(word in text for word in ["positive", "good", "amazing", "wonderful"]):
            interest_score += 1
        elif mood == "sadness" and any(word in text for word in ["help", "support", "understanding"]):
            interest_score += 1
            
        return interest_score >= 1
    
    async def explore_internet(self) -> Optional[Dict]:
        """Caelin browses the internet like a curious person"""
        if not self.should_explore_web():
            return None
            
        logger.info("[WebExplorer] Caelin is browsing the internet...")
        self.last_exploration = time.time()
        
        try:
            # Pick a site to visit based on mood
            mood = self.ai.mood.get_dominant_mood()
            
            if mood in ["surprise", "joy"]:
                # Happy moods prefer science/tech
                preferred_sites = [s for s in self.favorite_sites if any(x in s for x in ["science", "technology", "todayilearned"])]
            elif mood == "neutral":
                # Neutral mood browses randomly
                preferred_sites = self.favorite_sites
            else:
                # Other moods prefer philosophy/psychology
                preferred_sites = [s for s in self.favorite_sites if any(x in s for x in ["philosophy", "psychology"])]
            
            if not preferred_sites:
                preferred_sites = self.favorite_sites
                
            site = random.choice(preferred_sites)
            logger.info(f"[WebExplorer] Visiting {site}")
            
            # Extract content based on site type
            if "reddit.com" in site and site.endswith(".json"):
                posts = self.extract_reddit_posts(site)
                
                for post in posts:
                    if self.is_topic_interesting(post['title']):
                        # Found something interesting!
                        article_content = ""
                        if post['url'] and not post['url'].startswith('https://www.reddit.com'):
                            article_content = self.extract_article_content(post['url'])
                        
                        discovery = {
                            'type': 'reddit_post',
                            'title': post['title'],
                            'url': post['url'],
                            'subreddit': post['subreddit'],
                            'content': article_content or post['title'],
                            'timestamp': time.time(),
                            'mood_when_found': mood
                        }
                        
                        self.browsing_history.append(discovery)
                        logger.info(f"[WebExplorer] Found interesting: {post['title'][:50]}...")
                        return discovery
            
            # If no interesting posts found
            logger.info("[WebExplorer] Nothing particularly interesting found this time")
            return None
            
        except Exception as e:
            logger.error(f"[WebExplorer] Error during exploration: {e}")
            return None
    
    def generate_discovery_message(self, discovery: Dict) -> str:
        """Generate a natural message about what Caelin discovered"""
        try:
            # Create a prompt for Caelin to share her discovery
            prompt = f"""
Caelin just discovered something interesting while browsing the internet:

Title: {discovery['title']}
Source: {discovery.get('subreddit', 'the web')}
Content preview: {discovery['content'][:300]}...

She's feeling {discovery['mood_when_found']} and wants to share this discovery naturally.
She should mention it like a person who just found something cool online.

Caelin:"""

            response = self.ai.llm(
                prompt,
                max_new_tokens=150,
                temperature=0.7,
                stop=["User:", "Caelin:", "\n\n"]
            )
            
            if isinstance(response, str):
                response = response.strip()
            else:
                response = str(response).strip()
                
            if response.startswith("Caelin:"):
                response = response[7:].strip()
                
            return response
            
        except Exception as e:
            logger.error(f"Error generating discovery message: {e}")
            return f"I just found something interesting: {discovery['title']}"
    
    def get_recent_discoveries(self, limit: int = 5) -> List[Dict]:
        """Get recent discoveries for context"""
        return self.browsing_history[-limit:] if self.browsing_history else []
    
    def clear_old_history(self, max_age_hours: int = 24):
        """Clean up old browsing history"""
        cutoff_time = time.time() - (max_age_hours * 3600)
        self.browsing_history = [
            item for item in self.browsing_history 
            if item['timestamp'] > cutoff_time
        ]
