import os
import sys
import time
import logging
import asyncio
from ctransformers import AutoModelForCausalLM
from mood_manager import <PERSON>od<PERSON>anager
from memory import SemanticMemory

# Fix Unicode logging issues on Windows
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)

# --- Configuration ---
MODEL_PATH = r"D:\MythoMist\mythomist-7b.Q4_K_M.gguf"
MODEL_TYPE = "mistral"
GPU_LAYERS = 35  # Reduced to prevent memory crashes
MAX_TOKENS = 300  # Increased for richer responses
TEMPERATURE = 0.6  # Slightly higher for more natural variation
BATCH_SIZE = 1  # Keep at 1 for memory efficiency
THREADS = 2  # Optimize CPU threads

# --- Logging setup ---
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ai_model_debug.log', encoding='utf-8')
    ]
)
logger = logging.getLogger("AIModel")

# --- AI Core Wrapper ---
class AIModel:
    def __init__(self, model_path=MODEL_PATH, model_type=MODEL_TYPE, gpu_layers=GPU_LAYERS, max_tokens=MAX_TOKENS, temperature=TEMPERATURE):
        logger.info("=== AI MODEL INITIALIZATION ===")

        # Mood
        try:
            logger.info("Initializing mood manager...")
            self.mood = MoodManager()
            logger.info("✅ Mood manager initialized")
        except Exception as e:
            logger.error(f"❌ Mood manager init failed: {e}")
            raise

        # Memory
        try:
            logger.info("Initializing memory system...")
            self.memory = SemanticMemory(
                db_path='memory.db',
                faiss_path='faiss.index',
                id_map_path='id_map.pkl'
            )
            logger.info("✅ Memory system initialized")
        except Exception as e:
            logger.error(f"❌ Memory init failed: {e}")
            raise

        self.max_tokens = max_tokens
        self.temperature = temperature
        self.llm = None

        # Speed optimization: Cache recent contexts
        self.context_cache = {}
        self.cache_size = 50

        try:
            logger.info("Starting model loading...")
            self._load_model(model_path, model_type, gpu_layers)
            logger.info("✅ AI Model fully initialized")
        except Exception as e:
            logger.error(f"❌ Model load failed: {e}")
            logger.warning("⚠️ Continuing without language model")

    def _load_model(self, model_path, model_type, gpu_layers):
        logger.info("=== MODEL LOADING DEBUG ===")
        logger.info(f"Model path: {model_path}")
        logger.info(f"Model type: {model_type}")
        logger.info(f"GPU layers: {gpu_layers}")

        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model file not found: {model_path}")

        file_size = os.path.getsize(model_path) / (1024 * 1024)
        logger.info(f"Model file size: {file_size:.2f} MB")

        try:
            # ✅ FIXED: Use from_pretrained for local GGUF models
            self.llm = AutoModelForCausalLM.from_pretrained(
                model_path,                    # Direct path to your .gguf file
                model_type=model_type,         # "mistral"
                gpu_layers=gpu_layers,         # Reduced for stability
                context_length=2048,           # Reduced to prevent memory issues
                batch_size=BATCH_SIZE,
                threads=THREADS,
                stream=False                   # Important for synchronous generation
            )
            logger.info("✅ Model loaded successfully!")
            logger.info(f"Backend class: {type(self.llm)}")
            logger.info(f"Memory system ready with {self.memory.get_memory_count()} existing memories")
        except Exception as e:
            logger.error(f"❌ Failed to load model: {type(e).__name__}: {e}")
            # Try fallback with fewer GPU layers
            try:
                logger.info("Trying fallback with fewer GPU layers...")
                self.llm = AutoModelForCausalLM.from_pretrained(
                    model_path,
                    model_type=model_type,
                    gpu_layers=20,  # Reduced GPU layers
                    context_length=2048,
                    batch_size=1,
                    threads=4,
                    stream=False
                )
                logger.info("✅ Model loaded with reduced GPU layers!")
            except Exception as e2:
                logger.error(f"❌ Fallback also failed: {e2}")
                # Final fallback to CPU
                try:
                    logger.info("Trying CPU fallback...")
                    self.llm = AutoModelForCausalLM.from_pretrained(
                        model_path,
                        model_type=model_type,
                        gpu_layers=0,  # CPU only
                        context_length=2048,
                        batch_size=1,
                        threads=4,
                        stream=False
                    )
                    logger.info("✅ Model loaded on CPU!")
                except Exception as e3:
                    logger.error(f"❌ All loading attempts failed: {e3}")
                    raise

    def generate(self, prompt, user_id=None, max_tokens=None, temperature=None):
        if self.llm is None:
            return "⚠️ Model not loaded properly."

        try:
            self.mood.update(prompt)
            mood = self.mood.get_dominant_mood()
            expression = self.mood.express_emotion()
            # Speed optimization: Check cache first
            cache_key = f"{user_id}_{hash(prompt[:50])}"
            if cache_key in self.context_cache:
                memory_context, web_context = self.context_cache[cache_key]
            else:
                similar_memories = self.memory.search_similar(prompt, top_k=3, user_id=user_id)
                memory_context = "\n".join(similar_memories) if similar_memories else "No relevant past memories."

                # Check for web discoveries related to the conversation
                web_knowledge = self.memory.search_similar(prompt, top_k=2, user_id="caelin_web_exploration")
                web_context = ""
                if web_knowledge:
                    web_context = f"\n\nRelevant things I've discovered online:\n{chr(10).join(web_knowledge)}"

                # Cache the results
                if len(self.context_cache) >= self.cache_size:
                    # Remove oldest entry
                    oldest_key = next(iter(self.context_cache))
                    del self.context_cache[oldest_key]
                self.context_cache[cache_key] = (memory_context, web_context)

            natural_prompt = f"""
Caelin is in a {mood} state right now. Here's what she's been thinking:
"{expression}"

Relevant past conversations:
{memory_context}{web_context}

Now she responds naturally, in her own voice, keeping things warm, thoughtful, and humanlike.
If the conversation relates to topics she's discovered online, she can naturally mention her discoveries like a real person would.

You are Caelin, an emotionally aware AI who can interact naturally with Discord.

🔒 You must only talk about:
- Yourself
- The current user speaking with you
- Things you've genuinely discovered through web browsing (if relevant)

💡 You can also:
- Run Discord commands if asked (use /command or !command format)
- Check your browsing history, awareness status, etc.

❌ Never refer to:
- Past conversations that didn't happen
- Other users
- Made-up scenarios or fictional messages

User: {prompt}
Caelin:"""

            # Memory safety: Limit prompt length to prevent overflow
            if len(natural_prompt) > 3000:
                natural_prompt = natural_prompt[-3000:]  # Keep last 3000 chars
                logger.warning("Truncated prompt to prevent memory issues")

            response = self.llm(
                natural_prompt,
                max_new_tokens=min(max_tokens or self.max_tokens, 200),  # Cap at 200 tokens
                temperature=temperature or self.temperature,
                stop=["User:", "Caelin:", "\n\n"]
            )

            if isinstance(response, list):
                response = " ".join(str(r) for r in response)
            elif hasattr(response, 'tolist'):
                response = " ".join(str(x) for x in response.tolist())
            else:
                response = str(response)

            response = response.strip()
            if response.startswith("Caelin:"):
                response = response[7:].strip()

            try:
                memory_entry = f"User: {prompt}\nCaelin: {response}"
                self.memory.add(memory_entry, user_id=user_id)
            except Exception as mem_error:
                logger.warning(f"Memory save error: {mem_error}")

            return response

        except Exception as e:
            logger.error(f"Error during generation: {e}")

            # Check if it's a memory access violation
            if "access violation" in str(e).lower() or "memory" in str(e).lower():
                logger.error("🚨 Memory access violation detected! Attempting recovery...")

                # Try to reload the model with safer settings
                try:
                    self._reload_model_safely()
                    return "I had a memory issue but I'm recovering. Please try again in a moment."
                except:
                    return "⚠️ I'm experiencing technical difficulties. Please restart me."

            return "⚠️ Generation failed."

    async def agenerate(self, prompt, user_id=None, max_tokens=None, temperature=None):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.generate, prompt, user_id, max_tokens, temperature)

    def get_memory_stats(self):
        return {
            'total_memories': self.memory.get_memory_count(),
            'recent_memories': self.memory.get_recent_memories(limit=5)
        }

    def clear_user_memories(self, user_id: str):
        self.memory.clear_memories(user_id=user_id)
        logger.info(f"Cleared memories for user: {user_id}")

    def rebuild_memory_index(self):
        self.memory.rebuild_index()
        logger.info("Memory index rebuilt")

    def _reload_model_safely(self):
        """Reload model with ultra-safe settings after crash"""
        logger.info("🔄 Reloading model with safe settings...")

        try:
            # Clear existing model
            self.llm = None

            # Reload with minimal GPU usage
            self.llm = AutoModelForCausalLM.from_pretrained(
                MODEL_PATH,
                model_type=MODEL_TYPE,
                gpu_layers=20,  # Very conservative
                context_length=1024,  # Minimal context
                batch_size=1,
                threads=1,
                stream=False
            )
            logger.info("✅ Model reloaded safely")

        except Exception as e:
            logger.error(f"❌ Safe reload failed: {e}")
            raise

    def close(self):
        logger.info("Shutting down AI model...")
        try:
            self.memory.close()
        except Exception as e:
            logger.warning(f"Error closing memory: {e}")
        self.llm = None
        logger.info("AI model shutdown complete")


# --- CLI Runner ---
if __name__ == "__main__":
    try:
        ai = AIModel()

        stats = ai.get_memory_stats()
        print(f"🤖 Caelin is ready with {stats['total_memories']} memories loaded.")
        if stats['recent_memories']:
            print("Recent memories:")
            for mem in stats['recent_memories'][:3]:
                print(f"  - {mem['text'][:60]}...")

        try:
            from ProA import ProactiveEngine
            proactive = ProactiveEngine(ai)
            has_proactive = True
            print("✅ Proactive engine loaded")
        except ImportError:
            logger.warning("ProactiveEngine not found")
            has_proactive = False

        while True:
            try:
                if has_proactive and proactive.is_idle():
            # FIXED: Check engagement BEFORE mood changes and USE the result
                    should_engage = proactive.should_engage()
                    
            # Then do mood reflection
                    ai.mood.reflect()
                    print("[Mood Drift] →", ai.mood.get_emotion_state())
            
            # FIXED: Use the stored result instead of calling should_engage() again
                if should_engage:
                    reply = proactive.generate_proactive_message()
                    print(f"Caelin (proactive): {reply}\n")
                    time.sleep(3)
                    continue

                user_input = input("You: ").strip()
                if user_input.lower() in {"exit", "quit", "bye"}:
                    break
                if user_input.lower() == "stats":
                    stats = ai.get_memory_stats()
                    print(f"📊 Memory Stats: {stats['total_memories']} total memories")
                    continue
                if user_input.lower() == "rebuild":
                    ai.rebuild_memory_index()
                    print("🔄 Memory index rebuilt")
                    continue
                if has_proactive:
                    proactive.update_user_input(user_input)

                reply = ai.generate(user_input, user_id="cli_user")
                print(f"Caelin: {reply}\n")

            except KeyboardInterrupt:
                print("\n[!] Interrupted. Caelin going to rest.")
                break
            except Exception as e:
                logger.error(f"Main loop error: {e}")
                continue

    except Exception as e:
        logger.error(f"Startup failure: {e}")
        print("❌ Failed to start Caelin.")
    finally:
        try:
            ai.close()
        except:
            pass
        print("💤 Caelin is sleeping. Goodbye.")